import customtkinter as ctk
from pymem import Pymem
from pymem.pattern import pattern_scan_module
import psutil
import struct
import time
import threading
import win32api
import win32con
import keyboard
import re
import json
import os
import sys
import platform
import ctypes
import win32gui
import pymem  # Importiere pymem direkt in der Methode
import tkinter.ttk as ttk
import tkinter as tk
import base64
import io
from PIL import Image
from pathlib import Path
import requests
import pygame
import urllib.parse

# Import protection modules
try:
    from client_core_module import ClientCore
    from client_protection import ClientProtectionManager
    CLIENT_PROTECTION_LOADED = True
except ImportError:
    CLIENT_PROTECTION_LOADED = False
    print("Client protection modules not found - running in development mode")

# Embedded Fusion icon as Base64 (obfuscated)
def _get_client_icon_data():
    """Dynamically decode icon data"""
    import base64
    parts = [
        "iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAACkElEQVR4nOWYvW4TQRSFvzk7a8eIR0hBkHgJpLwBEh1QIfEIKREhiNDQgGh4BISoCBSUFKShpaBFUFBDA7azf2iWDbJQing9u2vrfo",
        "21subunDN7Z+4dt71zpcIwwjjCOMI4wjjCOMI4wjjCOMI4wjjCOMI4wjjCOMI4wjjCOL6LoDlQDSDEtRwXXfzlSkx6NCEFvriK31RLm+BjTmQK3CxT9vNxPak+DAhGHynnwM9ajfcxxd8oUw7zMQlQ0j0THEfK2PczTqB+7yAGTB",
        "vxjxrxBf2s/Gtl3PUzspbio5wCw4gPK5+vLH5lA4YTv/rKr5wC0zPEtzmGlmGr3vDiiW9tQBB7q0x5mI/r57KHiipMNNZn/3/cpc/5nUrcy8dcAN67guf+pDagSxPCkfrZFVHFtzIgDPjqSh77OQ/yMbtVwqfS8ySZ1xPrMg3SyO",
        "Jbp0BY6RfKKBM4LMbsFaM60LPGhE1CbQa5ZkN6mWQc+DkZFddLz0VcLwVQTHzbgf9MUIb3cKcYbWRr6VcZvGjCd8p6o+r6KIyNXzWAazanDyrq3/CctSyKRgNcUPhYgYL4QNgDrlYJ25WWMiGI/+gKvrmy143Uxw4Y6oTbRcq1Mm",
        "V2job4bxo53ijnnc97TyHfRdCQAkH8/Jy1/avI5e0yiAGJ3dhslAGTDhqbjTFgErGfX8s94BR3hsOjNfjsOzdAjbBwZ7B4Uxtedqyc+2sivhMDXNMo7Zaet0nGU53U+X7KT1fV9cE6iO/EgFAQHatgz8+4VIkfruLXwv9d3xusRQ",
        "pshYsS5fWFyeLqryPq0tnzFEJDoy6Db0JnKIwjjCOMI4wjjCOMI4wjjCOMI4wjjCOMI4wjjCOMI4yjoScwNH8AJEC7uVxsAXkAAAAASUVORK5CYII="
    ]
    return ''.join(parts)

FUSION_ICON_BASE64 = _get_client_icon_data()

class FusionClient:
    def __init__(self):
        # Initialize protection system first
        self.protection_enabled = CLIENT_PROTECTION_LOADED
        self.client_core = None

        if self.protection_enabled:
            try:
                # Verify execution environment - but don't fail hard
                protection_manager = ClientProtectionManager()
                if not protection_manager.is_safe_environment():
                    print("Client Protection: Environment check failed, but continuing with protection")
                    # Don't raise exception, just continue

                # Initialize client core with protection
                self.client_core = ClientCore()

                # Get protected configuration
                protected_config = self.client_core.get_runtime_config()

                # Use protected data
                self.protected_sound_urls = protected_config['sound_urls']
                self.protected_memory_patterns = protected_config['memory_patterns']
                self.protected_static_offsets = protected_config['static_offsets']

                print("Client Protection: Successfully loaded protected configuration")

            except Exception as e:
                print(f"Client protection system failed: {e}")
                print("Client Protection: Falling back to unprotected mode")
                self.protection_enabled = False
                self._use_fallback_config()
        else:
            print("Client Protection: Modules not available, using fallback configuration")
            self._use_fallback_config()

        self.pm = None
        self.air_jump_address = None
        self.fast_ladder_address = None
        self.phase_address = None
        self.no_web_address = None
        self.fly_address = None
        self.antikb_address1 = None
        self.antikb_address2 = None
        self.zoom_address = None
        self.see_entities_address = None
        self.no_hurtcam_address = None
        self.reach_address = None

        # Hotkey variables
        self.hotkeys = {
            "air_jump": "",
            "fast_ladder": "",
            "phase": "",
            "no_web": "",
            "high_jump": "",
            "fly": "",
            "antikb": "",
            "auto_clicker": "",
            "semi_scaffold": "",
            "rapid_hit": "",
            "zoom": "",
            "see_entities": "",
            "no_hurtcam": "",
            "fast_place": "",
            "reach": "",
            "s_tap": "",
            "auto_sprint": ""
        }
        self.hotkey_listener_active = False
        self.hotkey_listeners = []

        # Memory patterns - will be set by protection system or fallback
        self._init_memory_patterns()

        # Default values
        self.high_jump_value = 1.0  # Standardwert
        self.reach_value = 3.0  # Default value (range 3-7)

    def _init_memory_patterns(self):
        """Initialize memory patterns from protected configuration"""
        if hasattr(self, 'protected_memory_patterns'):
            patterns = self.protected_memory_patterns

            # AirJump patterns
            self.original_bytes = patterns.get('air_jump_pattern', bytes([0x0F, 0x2E, 0x77, 0x34, 0x75, 0x0D]))
            self.patched_bytes = patterns.get('air_jump_patch', bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x90]))

            # FastLadder patterns
            self.original_ladder_bytes = patterns.get('fast_ladder_pattern', bytes([0xC7, 0x47, 0x1C, 0xCD, 0xCC, 0x4C, 0x3E]))

            # HighJump patterns
            self.original_high_jump_bytes = patterns.get('high_jump_pattern', bytes([0x89, 0x01, 0x41, 0xF6, 0x04, 0x24, 0x08]))

            # Phase patterns
            self.original_phase_bytes = patterns.get('phase_pattern', bytes([0xF2, 0x0F, 0x11, 0x4A, 0x10, 0x48, 0x83, 0x43, 0x08, 0x18, 0x48, 0x8B, 0x4C, 0x24, 0x58, 0x48, 0x33, 0xCC, 0xE8, 0x97]))
            self.phase_patched_bytes = patterns.get('phase_patch', bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x48, 0x83, 0x43, 0x08, 0x18, 0x48, 0x8B, 0x4C, 0x24, 0x58, 0x48, 0x33, 0xCC, 0xE8, 0x97]))

            # NoWeb patterns
            self.original_no_web_bytes = patterns.get('no_web_pattern', bytes([0xF3, 0x0F, 0x10, 0x2F, 0xF3, 0x0F, 0x10, 0x0D, 0x77, 0x57, 0xF9, 0x02]))
            self.no_web_patched_bytes = patterns.get('no_web_patch', bytes([0x90, 0x90, 0x90, 0x90]))

            # Fly patterns
            self.original_fly_bytes = patterns.get('fly_pattern', bytes([0x40, 0x38, 0x71, 0x04, 0x75, 0x64]))
            self.fly_patched_bytes = patterns.get('fly_patch', bytes([0xC6, 0x41, 0x04, 0x01, 0x90, 0x90]))

            # AntiKb patterns
            self.original_antikb_bytes1 = patterns.get('antikb_pattern1', bytes([0xF2, 0x0F, 0x11, 0x40, 0x18, 0x44]))
            self.patched_antikb_bytes1 = patterns.get('antikb_patch1', bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x44]))
            self.original_antikb_bytes2 = patterns.get('antikb_pattern2', bytes([0x44, 0x89, 0x40, 0x20, 0x48, 0x83, 0xC4, 0x28]))
            self.patched_antikb_bytes2 = patterns.get('antikb_patch2', bytes([0x90, 0x90, 0x90, 0x90, 0x48, 0x83, 0xC4, 0x28]))

            # Scaffold patterns
            self.original_scaffold_bytes1 = patterns.get('scaffold_pattern1', bytes([0x74, 0xE2, 0x49, 0x8B, 0x80, 0x18, 0x02, 0x00, 0x00]))
            self.patched_scaffold_bytes1 = patterns.get('scaffold_patch1', bytes([0x90, 0x90, 0x49, 0x8B, 0x80, 0x18, 0x02, 0x00, 0x00]))
            self.original_scaffold_bytes2 = patterns.get('scaffold_pattern2', bytes([0x74, 0x19, 0x48, 0x8B, 0x03, 0x48, 0x8D]))
            self.patched_scaffold_bytes2 = patterns.get('scaffold_patch2', bytes([0x90, 0x90, 0x48, 0x8B, 0x03, 0x48, 0x8D]))

            # Rapid Hit patterns
            self.original_rapid_hit_bytes = patterns.get('rapid_hit_pattern', bytes([0x45, 0x38, 0x30, 0x0F, 0x84, 0xC1, 0x00, 0x00, 0x00]))
            self.patched_rapid_hit_bytes = patterns.get('rapid_hit_patch', bytes([0x90, 0x90, 0x90, 0x0F, 0x84, 0xC1, 0x00, 0x00, 0x00]))

            # Zoom patterns
            self.original_zoom_bytes = patterns.get('zoom_pattern', bytes([0xF3, 0x0F, 0x10, 0x48, 0x18, 0x48]))
            self.patched_zoom_bytes = patterns.get('zoom_patch', bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x48]))

            # See Entities patterns
            self.original_see_entities_bytes = patterns.get('see_entities_pattern', bytes([0x44, 0x8B, 0x4A, 0x04, 0x44, 0x8B, 0x42, 0x08, 0x48]))
            self.patched_see_entities_bytes = patterns.get('see_entities_patch', bytes([0x90, 0x90, 0x90, 0x90, 0x44, 0x8B, 0x42, 0x08, 0x48]))

            # No HurtCam patterns
            self.original_no_hurtcam_bytes = patterns.get('no_hurtcam_pattern', bytes([0xC7, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x89]))
            self.patched_no_hurtcam_bytes = patterns.get('no_hurtcam_patch', bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x89]))

            # FastPlace patterns
            self.original_fast_place_bytes = patterns.get('fast_place_pattern', bytes([0x0F, 0x2E, 0x47, 0x34, 0x75, 0x0D]))
            self.patched_fast_place_bytes = patterns.get('fast_place_patch', bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x90]))

            # AutoSprint patterns
            self.original_auto_sprint_bytes = patterns.get('auto_sprint_pattern', bytes([0x41, 0x88, 0x41, 0x08, 0x0F, 0xB6, 0x42, 0x31]))
            self.patched_auto_sprint_bytes = patterns.get('auto_sprint_patch', bytes([0x41, 0xC6, 0x41, 0x08, 0x01, 0x0F, 0xB6, 0x42]))

            # Static offsets
            if hasattr(self, 'protected_static_offsets'):
                self.reach_offset = self.protected_static_offsets.get('reach_offset', 0x7AF3120)
            else:
                self.reach_offset = 0x7AF3120
        else:
            # Fallback patterns (will be set by _use_fallback_config)
            pass

        self.scaffold_address1 = None
        self.scaffold_address2 = None
        self.is_semi_scaffold_enabled = False
        self.rapid_hit_address = None
        self.is_rapid_hit_enabled = False

        self.is_air_jump_enabled = False
        self.is_fast_ladder_enabled = False
        self.is_phase_enabled = False
        self.is_no_web_enabled = False
        self.is_high_jump_enabled = False
        self.is_fly_enabled = False
        self.is_antikb_enabled = False
        self.is_zoom_enabled = False
        self.is_see_entities_enabled = False
        self.is_no_hurtcam_enabled = False
        self.is_reach_enabled = False
        self.is_s_tap_enabled = False
        self.zoom_hold_mode = True  # Default to hold mode
        self.fast_ladder_value = 1.0

        # Ensure all critical attributes are always initialized
        if not hasattr(self, 'high_jump_value'):
            self.high_jump_value = 1.0
        if not hasattr(self, 'reach_value'):
            self.reach_value = 3.0
        if not hasattr(self, 'auto_clicker_cps'):
            self.auto_clicker_cps = 10
        if not hasattr(self, 'fast_place_cps'):
            self.fast_place_cps = 10

        # FastPlace variables
        self.is_fast_place_enabled = False
        self.fast_place_cps = 10  # Standard CPS
        self.fast_place_thread = None
        self.should_fast_place = False
        self.fast_place_address = None

        # FastPlace Pattern (unterschiedlich von AirJump)
        self.original_fast_place_bytes = bytes([0x0F, 0x2E, 0x47, 0x34, 0x75, 0x0D])  # Original bytes (anderes Pattern)
        self.patched_fast_place_bytes = bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x90])  # NOPs

        self.is_auto_clicker_enabled = False
        self.auto_clicker_cps = 10  # Standard CPS
        self.auto_clicker_thread = None
        self.should_auto_click = False



        # Double Click variables
        self.is_double_click_enabled = False
        self.double_click_thread = None
        self.last_left_click_time = 0
        self.double_click_delay = 0.02  # 20ms delay between clicks

        # S-Tap variables
        self.is_s_tap_enabled = False
        self.s_tap_thread = None

        # AutoSprint Pattern
        self.original_auto_sprint_bytes = bytes([0x41, 0x88, 0x41, 0x08, 0x0F, 0xB6, 0x42, 0x31])  # Original bytes
        self.patched_auto_sprint_bytes = bytes([0x41, 0xC6, 0x41, 0x08, 0x01, 0x0F, 0xB6, 0x42])  # Patched bytes (mov [r9+08],1)
        self.auto_sprint_address = None
        self.is_auto_sprint_enabled = False

        # Sound settings
        self.sounds_enabled = True
        self.sounds_downloaded = False
        self.enable_sound_path = None
        self.disable_sound_path = None

        # Sound URLs - will be set by protection system or fallback
        self.enable_sound_url = None
        self.disable_sound_url = None
        self._set_sound_urls()

        # Initialize pygame mixer for sound playback
        try:
            pygame.mixer.init()
        except Exception as e:
            print(f"Failed to initialize pygame mixer: {e}")
            self.sounds_enabled = False

    def _use_fallback_config(self):
        """Use fallback configuration when protection is not available"""
        # Fallback sound URLs (less secure but functional)
        self.protected_sound_urls = {
            'enable_sound_url': "https://www.dropbox.com/scl/fi/u4f0kjepvnj9jtsm91zp3/ENABLE.wav?rlkey=19k0sx3m2yfgq8215kadhu3u8&st=re585s91&dl=1",
            'disable_sound_url': "https://www.dropbox.com/scl/fi/ool3u4yhqbl2eztrac1c8/DISABLE.wav?rlkey=hrf3kvgfkk05itmjgnvknqa1w&st=qevknf9l&dl=1"
        }

        # Fallback memory patterns (original unprotected patterns)
        self.protected_memory_patterns = {
            'air_jump_pattern': bytes([0x0F, 0x2E, 0x77, 0x34, 0x75, 0x0D]),
            'air_jump_patch': bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x90]),
            'fast_ladder_pattern': bytes([0xC7, 0x47, 0x1C, 0xCD, 0xCC, 0x4C, 0x3E]),
            'high_jump_pattern': bytes([0x89, 0x01, 0x41, 0xF6, 0x04, 0x24, 0x08]),
            'phase_pattern': bytes([0xF2, 0x0F, 0x11, 0x4A, 0x10, 0x48, 0x83, 0x43, 0x08, 0x18, 0x48, 0x8B, 0x4C, 0x24, 0x58, 0x48, 0x33, 0xCC, 0xE8, 0x97]),
            'phase_patch': bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x48, 0x83, 0x43, 0x08, 0x18, 0x48, 0x8B, 0x4C, 0x24, 0x58, 0x48, 0x33, 0xCC, 0xE8, 0x97]),
            'no_web_pattern': bytes([0xF3, 0x0F, 0x10, 0x2F, 0xF3, 0x0F, 0x10, 0x0D, 0x77, 0x57, 0xF9, 0x02]),
            'no_web_patch': bytes([0x90, 0x90, 0x90, 0x90]),
            'fly_pattern': bytes([0x40, 0x38, 0x71, 0x04, 0x75, 0x64]),
            'fly_patch': bytes([0xC6, 0x41, 0x04, 0x01, 0x90, 0x90]),
            'antikb_pattern1': bytes([0xF2, 0x0F, 0x11, 0x40, 0x18, 0x44]),
            'antikb_patch1': bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x44]),
            'antikb_pattern2': bytes([0x44, 0x89, 0x40, 0x20, 0x48, 0x83, 0xC4, 0x28]),
            'antikb_patch2': bytes([0x90, 0x90, 0x90, 0x90, 0x48, 0x83, 0xC4, 0x28]),
            'scaffold_pattern1': bytes([0x74, 0xE2, 0x49, 0x8B, 0x80, 0x18, 0x02, 0x00, 0x00]),
            'scaffold_patch1': bytes([0x90, 0x90, 0x49, 0x8B, 0x80, 0x18, 0x02, 0x00, 0x00]),
            'scaffold_pattern2': bytes([0x74, 0x19, 0x48, 0x8B, 0x03, 0x48, 0x8D]),
            'scaffold_patch2': bytes([0x90, 0x90, 0x48, 0x8B, 0x03, 0x48, 0x8D]),
            'rapid_hit_pattern': bytes([0x45, 0x38, 0x30, 0x0F, 0x84, 0xC1, 0x00, 0x00, 0x00]),
            'rapid_hit_patch': bytes([0x90, 0x90, 0x90, 0x0F, 0x84, 0xC1, 0x00, 0x00, 0x00]),
            'zoom_pattern': bytes([0xF3, 0x0F, 0x10, 0x48, 0x18, 0x48]),
            'zoom_patch': bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x48]),
            'see_entities_pattern': bytes([0x44, 0x8B, 0x4A, 0x04, 0x44, 0x8B, 0x42, 0x08, 0x48]),
            'see_entities_patch': bytes([0x90, 0x90, 0x90, 0x90, 0x44, 0x8B, 0x42, 0x08, 0x48]),
            'no_hurtcam_pattern': bytes([0xC7, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x89]),
            'no_hurtcam_patch': bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x89]),
            'fast_place_pattern': bytes([0x0F, 0x2E, 0x47, 0x34, 0x75, 0x0D]),
            'fast_place_patch': bytes([0x90, 0x90, 0x90, 0x90, 0x90, 0x90]),
            'auto_sprint_pattern': bytes([0x41, 0x88, 0x41, 0x08, 0x0F, 0xB6, 0x42, 0x31]),
            'auto_sprint_patch': bytes([0x41, 0xC6, 0x41, 0x08, 0x01, 0x0F, 0xB6, 0x42])
        }

        # Fallback static offsets
        self.protected_static_offsets = {
            'reach_offset': 0x7AF3120
        }

    def _set_sound_urls(self):
        """Set sound URLs from protected configuration"""
        if hasattr(self, 'protected_sound_urls'):
            self.enable_sound_url = self.protected_sound_urls['enable_sound_url']
            self.disable_sound_url = self.protected_sound_urls['disable_sound_url']
        else:
            # Fallback URLs
            self.enable_sound_url = "https://www.dropbox.com/scl/fi/u4f0kjepvnj9jtsm91zp3/ENABLE.wav?rlkey=19k0sx3m2yfgq8215kadhu3u8&st=re585s91&dl=1"
            self.disable_sound_url = "https://www.dropbox.com/scl/fi/ool3u4yhqbl2eztrac1c8/DISABLE.wav?rlkey=hrf3kvgfkk05itmjgnvknqa1w&st=qevknf9l&dl=1"



        # GUI Setup
        self.root = ctk.CTk()
        self.root.title("Fusion Client")
        self.root.geometry("900x650")  # Größere GUI
        self.root.resizable(False, False)

        # Load window icon
        self.load_window_icon()

        # Color themes
        self.color_themes = {
            "Green": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#00ff9d",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#00ff9d",
                "switch_off": "#404040"
            },
            "Blue": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#00aaff",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#00aaff",
                "switch_off": "#404040"
            },
            "Red": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#ff4444",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#ff4444",
                "switch_off": "#404040"
            },
            "Purple": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#aa44ff",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#aa44ff",
                "switch_off": "#404040"
            },
            "Orange": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#ff8800",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#ff8800",
                "switch_off": "#404040"
            },
            "Cyan": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#00ffff",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#00ffff",
                "switch_off": "#404040"
            },
            "Pink": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#ff69b4",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#ff69b4",
                "switch_off": "#404040"
            },
            "Yellow": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#ffff00",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#ffff00",
                "switch_off": "#404040"
            },
            "Lime": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#32ff32",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#32ff32",
                "switch_off": "#404040"
            },
            "Magenta": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#ff00ff",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#ff00ff",
                "switch_off": "#404040"
            },
            "Teal": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#008080",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#008080",
                "switch_off": "#404040"
            },
            "Violet": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#8a2be2",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#8a2be2",
                "switch_off": "#404040"
            },
            "Crimson": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#dc143c",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#dc143c",
                "switch_off": "#404040"
            },
            "Gold": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#ffd700",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#ffd700",
                "switch_off": "#404040"
            },
            "Aqua": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#00bfff",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#00bfff",
                "switch_off": "#404040"
            },
            "Coral": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#ff7f50",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#ff7f50",
                "switch_off": "#404040"
            },
            "Indigo": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#4b0082",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#4b0082",
                "switch_off": "#404040"
            },
            "Mint": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#98fb98",
                "text": "#ffffff",
                "text_secondary": "#b3b3b3",
                "switch_on": "#98fb98",
                "switch_off": "#404040"
            },
            "Neon Green": {
                "bg": "#0a0a0a",
                "frame_bg": "#1a1a1a",
                "accent": "#39ff14",
                "text": "#ffffff",
                "text_secondary": "#ccffcc",
                "switch_on": "#39ff14",
                "switch_off": "#404040"
            },
            "Electric Blue": {
                "bg": "#0a0a1a",
                "frame_bg": "#151530",
                "accent": "#7df9ff",
                "text": "#ffffff",
                "text_secondary": "#e0ffff",
                "switch_on": "#7df9ff",
                "switch_off": "#404040"
            },
            "Hot Pink": {
                "bg": "#1a0a1a",
                "frame_bg": "#301530",
                "accent": "#ff1493",
                "text": "#ffffff",
                "text_secondary": "#ffb6c1",
                "switch_on": "#ff1493",
                "switch_off": "#404040"
            },
            "Lava": {
                "bg": "#2d0a00",
                "frame_bg": "#200700",
                "accent": "#ff4500",
                "text": "#ffffff",
                "text_secondary": "#ffa500",
                "switch_on": "#ff4500",
                "switch_off": "#404040"
            },
            "Ice": {
                "bg": "#0a1a2d",
                "frame_bg": "#071320",
                "accent": "#b0e0e6",
                "text": "#ffffff",
                "text_secondary": "#f0f8ff",
                "switch_on": "#b0e0e6",
                "switch_off": "#404040"
            },
            "Galaxy": {
                "bg": "#0d0d2d",
                "frame_bg": "#0a0a20",
                "accent": "#9370db",
                "text": "#ffffff",
                "text_secondary": "#dda0dd",
                "switch_on": "#9370db",
                "switch_off": "#404040"
            },
            "Toxic": {
                "bg": "#1a2d0a",
                "frame_bg": "#132007",
                "accent": "#adff2f",
                "text": "#ffffff",
                "text_secondary": "#f0fff0",
                "switch_on": "#adff2f",
                "switch_off": "#404040"
            },
            "Blood": {
                "bg": "#2d0000",
                "frame_bg": "#200000",
                "accent": "#8b0000",
                "text": "#ffffff",
                "text_secondary": "#ffb6c1",
                "switch_on": "#8b0000",
                "switch_off": "#404040"
            },
            "Royal": {
                "bg": "#0a0a2d",
                "frame_bg": "#070720",
                "accent": "#6a5acd",
                "text": "#ffffff",
                "text_secondary": "#e6e6fa",
                "switch_on": "#6a5acd",
                "switch_off": "#404040"
            },
            "Emerald": {
                "bg": "#0a2d1a",
                "frame_bg": "#072013",
                "accent": "#50c878",
                "text": "#ffffff",
                "text_secondary": "#f0fff0",
                "switch_on": "#50c878",
                "switch_off": "#404040"
            },
            "Sunset": {
                "bg": "#2d1a0a",
                "frame_bg": "#201007",
                "accent": "#ff6347",
                "text": "#ffffff",
                "text_secondary": "#ffd4c4",
                "switch_on": "#ff6347",
                "switch_off": "#404040"
            },
            "Ocean": {
                "bg": "#0a1a2d",
                "frame_bg": "#071320",
                "accent": "#1e90ff",
                "text": "#ffffff",
                "text_secondary": "#b0e0e6",
                "switch_on": "#1e90ff",
                "switch_off": "#404040"
            },
            "Forest": {
                "bg": "#0d2d0d",
                "frame_bg": "#0a200a",
                "accent": "#228b22",
                "text": "#ffffff",
                "text_secondary": "#90ee90",
                "switch_on": "#228b22",
                "switch_off": "#404040"
            },
            "Rainbow": {
                "bg": "#1a1a1a",
                "frame_bg": "#2d2d2d",
                "accent": "#ff0080",
                "text": "#ffffff",
                "text_secondary": "#ffccee",
                "switch_on": "#ff0080",
                "switch_off": "#404040"
            },
            "Neon Purple": {
                "bg": "#0a0a1a",
                "frame_bg": "#151530",
                "accent": "#bf00ff",
                "text": "#ffffff",
                "text_secondary": "#e6ccff",
                "switch_on": "#bf00ff",
                "switch_off": "#404040"
            },
            "Cyber": {
                "bg": "#001a1a",
                "frame_bg": "#003333",
                "accent": "#00ff80",
                "text": "#ffffff",
                "text_secondary": "#ccffee",
                "switch_on": "#00ff80",
                "switch_off": "#404040"
            },
            "Fire": {
                "bg": "#2d1a00",
                "frame_bg": "#201300",
                "accent": "#ff6600",
                "text": "#ffffff",
                "text_secondary": "#ffcc99",
                "switch_on": "#ff6600",
                "switch_off": "#404040"
            },
            "Midnight": {
                "bg": "#0a0a2d",
                "frame_bg": "#070720",
                "accent": "#4169e1",
                "text": "#ffffff",
                "text_secondary": "#b0c4de",
                "switch_on": "#4169e1",
                "switch_off": "#404040"
            }
        }

        # Default theme (Fusion standard color)
        self.current_theme = "Red"
        self.colors = self.color_themes[self.current_theme]

        # Load config after color themes are defined
        self.load_config()

        # Appearance Mode
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("dark-blue")

        # Configure custom styles
        self.configure_styles()

        # Current page tracking
        self.current_page = "main"

        # Initialize hotkey buttons dictionary
        self.hotkey_buttons = {}

        # Create main frame with glass effect
        self.main_frame = ctk.CTkFrame(
            self.root,
            fg_color=self.colors["bg"],
            corner_radius=0
        )
        self.main_frame.pack(fill="both", expand=True)

        # Header frame with gradient
        self.header_frame = ctk.CTkFrame(
            self.main_frame,
            fg_color=self.colors["frame_bg"],
            corner_radius=10
        )
        self.header_frame.pack(fill="x", pady=(0, 10))

        # Back button (initially hidden)
        self.back_button = ctk.CTkButton(
            self.header_frame,
            text="← Back",
            width=80,
            height=35,
            command=self.show_main_page,
            corner_radius=20,
            font=("Segoe UI", 14, "bold"),
            fg_color=self.colors["frame_bg"],
            hover_color=self.colors["accent"],
            text_color=self.colors["text"]
        )
        # Don't pack initially - will be shown when needed

        # Title with modern font
        self.title_label = ctk.CTkLabel(
            self.header_frame,
            text="FUSION CLIENT",
            font=("Segoe UI", 28, "bold"),
            text_color=self.colors["accent"]
        )
        self.title_label.pack(pady=15, side="left", padx=20)

        # Settings Button (Zahnrad)
        self.settings_button = ctk.CTkButton(
            self.header_frame,
            text="⚙️",
            width=40,
            height=40,
            command=self.open_settings,
            corner_radius=20,
            font=("Segoe UI", 20),
            fg_color=self.colors["frame_bg"],
            hover_color=self.colors["accent"]
        )
        self.settings_button.pack(pady=15, side="right", padx=20)

        # Status Label with modern style
        self.status_label = ctk.CTkLabel(
            self.main_frame,
            text="Searching Patterns...",
            font=("Segoe UI", 12),
            text_color=self.colors["text_secondary"]
        )
        self.status_label.pack(pady=5)

        # Create content container for page switching
        self.content_container = ctk.CTkFrame(
            self.main_frame,
            fg_color="transparent"
        )
        self.content_container.pack(fill="both", expand=True)

        # Create main page (categories)
        self.create_main_page()

        # Create settings page (initially hidden)
        self.create_settings_page()

        # Show main page initially
        self.show_main_page()

    def create_main_page(self):
        """Create the main client page with all modules"""
        self.main_page = ctk.CTkFrame(
            self.content_container,
            fg_color=self.colors["frame_bg"],
            corner_radius=10
        )

        # Categories Container (grid layout)
        self.categories_container = ctk.CTkFrame(
            self.main_page,
            fg_color=self.colors["frame_bg"],
            corner_radius=0
        )
        self.categories_container.pack(fill="both", expand=True, padx=10, pady=10)

        # Configure grid weights for equal distribution
        self.categories_container.grid_columnconfigure(0, weight=1)
        self.categories_container.grid_columnconfigure(1, weight=1)
        self.categories_container.grid_columnconfigure(2, weight=1)
        self.categories_container.grid_columnconfigure(3, weight=1)
        self.categories_container.grid_rowconfigure(0, weight=1)

        # Movement Category (column 0)
        self.movement_frame = ctk.CTkFrame(
            self.categories_container,
            fg_color=self.colors["bg"],
            corner_radius=8
        )
        self.movement_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")

        self.movement_label = ctk.CTkLabel(
            self.movement_frame,
            text="MOVEMENT",
            font=("Segoe UI", 16, "bold"),
            text_color=self.colors["accent"]
        )
        self.movement_label.pack(pady=5)

        # Combat Category (column 1)
        self.combat_frame = ctk.CTkFrame(
            self.categories_container,
            fg_color=self.colors["bg"],
            corner_radius=8
        )
        self.combat_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")

        self.combat_label = ctk.CTkLabel(
            self.combat_frame,
            text="COMBAT",
            font=("Segoe UI", 16, "bold"),
            text_color=self.colors["accent"]
        )
        self.combat_label.pack(pady=5)

        # Player Category (column 2)
        self.player_frame = ctk.CTkFrame(
            self.categories_container,
            fg_color=self.colors["bg"],
            corner_radius=8
        )
        self.player_frame.grid(row=0, column=2, padx=5, pady=5, sticky="nsew")

        self.player_label = ctk.CTkLabel(
            self.player_frame,
            text="PLAYER",
            font=("Segoe UI", 16, "bold"),
            text_color=self.colors["accent"]
        )
        self.player_label.pack(pady=5)

        # Visuals Category (column 3)
        self.visuals_frame = ctk.CTkFrame(
            self.categories_container,
            fg_color=self.colors["bg"],
            corner_radius=8
        )
        self.visuals_frame.grid(row=0, column=3, padx=5, pady=5, sticky="nsew")

        self.visuals_label = ctk.CTkLabel(
            self.visuals_frame,
            text="VISUALS",
            font=("Segoe UI", 16, "bold"),
            text_color=self.colors["accent"]
        )
        self.visuals_label.pack(pady=5)

        # Zoom Switch
        self.zoom_switch = ctk.CTkSwitch(
            self.visuals_frame,
            text="Zoom",
            width=120,
            command=lambda: self.ui_toggle_zoom()
        )
        self.zoom_switch.pack(pady=5, padx=20)
        self.zoom_switch.bind('<Button-3>', self.toggle_zoom_hold_frame)

        # Zoom Hold Mode Frame
        self.zoom_hold_frame = ctk.CTkFrame(self.visuals_frame)

        # Zoom Hold Mode Switch
        self.zoom_hold_switch = ctk.CTkSwitch(
            self.zoom_hold_frame,
            text="Hold Mode",
            width=120,
            command=self.toggle_zoom_hold_mode
        )
        self.zoom_hold_switch.pack(pady=5, padx=20)
        self.zoom_hold_switch.select()  # Default to hold mode

        # See Entities Switch (directly in visuals frame, no separate frame)
        self.see_entities_switch = ctk.CTkSwitch(
            self.visuals_frame,
            text="See Entities",
            width=120,
            command=lambda: self.ui_toggle_see_entities()
        )
        self.see_entities_switch.pack(pady=5, padx=20)

        # No HurtCam Switch
        self.no_hurtcam_switch = ctk.CTkSwitch(
            self.visuals_frame,
            text="No HurtCam",
            width=120,
            command=lambda: self.ui_toggle_no_hurtcam()
        )
        self.no_hurtcam_switch.pack(pady=5, padx=20)

        # Movement Modules
        self.air_jump_switch = ctk.CTkSwitch(
            self.movement_frame,
            text="AirJump",
            width=120,
            command=lambda: self.ui_toggle_air_jump()
        )
        self.air_jump_switch.pack(pady=5, padx=20)

        self.fly_switch = ctk.CTkSwitch(
            self.movement_frame,
            text="Fly",
            width=120,
            command=lambda: self.ui_toggle_fly()
        )
        self.fly_switch.pack(pady=5, padx=20)

        # Fast Ladder Switch
        self.fast_ladder_switch = ctk.CTkSwitch(
            self.movement_frame,
            text="FastLadder",
            width=120,
            command=lambda: self.ui_toggle_fast_ladder()
        )
        self.fast_ladder_switch.pack(pady=5, padx=20)
        self.fast_ladder_switch.bind('<Button-3>', self.toggle_value_slider)

        # Fast Ladder Value Frame
        self.fast_ladder_value_frame = ctk.CTkFrame(self.movement_frame)

        # Fast Ladder Slider
        self.fast_ladder_slider = ctk.CTkSlider(
            self.fast_ladder_value_frame,
            from_=1,
            to=20,
            number_of_steps=19,
            command=self.update_fast_ladder_value
        )
        self.fast_ladder_slider.pack(side="left", padx=5, fill="x", expand=True)
        self.fast_ladder_slider.set(self.fast_ladder_value)

        # Fast Ladder Value Label
        self.fast_ladder_value_label = ctk.CTkLabel(
            self.fast_ladder_value_frame,
            text="1.0"
        )
        self.fast_ladder_value_label.pack(side="right")

        # Phase Switch
        self.phase_switch = ctk.CTkSwitch(
            self.movement_frame,
            text="Phase",
            width=120,
            command=lambda: self.ui_toggle_phase()
        )
        self.phase_switch.pack(pady=5, padx=20)

        # High Jump Switch
        self.high_jump_switch = ctk.CTkSwitch(
            self.movement_frame,
            text="HighJump",
            width=120,
            command=lambda: self.ui_toggle_high_jump()
        )
        self.high_jump_switch.pack(pady=5, padx=20)
        self.high_jump_switch.bind('<Button-3>', self.toggle_high_jump_slider)

        # High Jump Value Frame
        self.high_jump_value_frame = ctk.CTkFrame(self.movement_frame)

        # High Jump Slider
        self.high_jump_slider = ctk.CTkSlider(
            self.high_jump_value_frame,
            from_=1,
            to=10,
            number_of_steps=9,
            command=self.update_high_jump_value
        )
        self.high_jump_slider.pack(side="left", padx=5, fill="x", expand=True)
        self.high_jump_slider.set(self.high_jump_value)

        # High Jump Value Label
        self.high_jump_value_label = ctk.CTkLabel(
            self.high_jump_value_frame,
            text="1.0"
        )
        self.high_jump_value_label.pack(side="right")

        # Semi-Scaffold Switch
        self.semi_scaffold_switch = ctk.CTkSwitch(
            self.movement_frame,
            text="Semi-Scaffold",
            width=120,
            command=lambda: self.ui_toggle_semi_scaffold()
        )
        self.semi_scaffold_switch.pack(pady=5, padx=20)

        # Player Modules
        self.no_web_switch = ctk.CTkSwitch(
            self.player_frame,
            text="NoWeb",
            width=120,
            command=lambda: self.ui_toggle_no_web()
        )
        self.no_web_switch.pack(pady=5, padx=20)

        # AntiKb Switch
        self.antikb_switch = ctk.CTkSwitch(
            self.player_frame,
            text="AntiKb",
            width=120,
            command=lambda: self.ui_toggle_antikb()
        )
        self.antikb_switch.pack(pady=5, padx=20)

        # FastPlace Switch
        self.fast_place_switch = ctk.CTkSwitch(
            self.player_frame,
            text="FastPlace",
            width=120,
            command=lambda: self.ui_toggle_fast_place()
        )
        self.fast_place_switch.pack(pady=5, padx=20)
        self.fast_place_switch.bind('<Button-3>', self.toggle_fast_place_slider)

        # FastPlace CPS Value Frame
        self.fast_place_value_frame = ctk.CTkFrame(self.player_frame)

        # FastPlace CPS Slider
        self.fast_place_slider = ctk.CTkSlider(
            self.fast_place_value_frame,
            from_=1,
            to=20,
            number_of_steps=19,
            command=self.update_fast_place_value
        )
        self.fast_place_slider.pack(side="left", padx=5, fill="x", expand=True)
        self.fast_place_slider.set(self.fast_place_cps)

        # FastPlace CPS Label
        self.fast_place_value_label = ctk.CTkLabel(
            self.fast_place_value_frame,
            text="10"
        )
        self.fast_place_value_label.pack(side="right")

        # AutoSprint Switch
        self.auto_sprint_switch = ctk.CTkSwitch(
            self.player_frame,
            text="AutoSprint",
            width=120,
            command=lambda: self.ui_toggle_auto_sprint()
        )
        self.auto_sprint_switch.pack(pady=5, padx=20)

        # Combat Modules
        # Rapid Hit Switch
        self.rapid_hit_switch = ctk.CTkSwitch(
            self.combat_frame,
            text="Rapid Hit",
            width=120,
            command=lambda: self.ui_toggle_rapid_hit()
        )
        self.rapid_hit_switch.pack(pady=5, padx=20)

        # Auto Clicker Switch
        self.auto_clicker_switch = ctk.CTkSwitch(
            self.combat_frame,
            text="AutoClicker",
            width=120,
            command=lambda: self.ui_toggle_auto_clicker()
        )
        self.auto_clicker_switch.pack(pady=5, padx=20)
        self.auto_clicker_switch.bind('<Button-3>', self.toggle_cps_slider)

        # CPS Value Frame
        self.cps_value_frame = ctk.CTkFrame(self.combat_frame)

        # CPS Slider
        self.cps_slider = ctk.CTkSlider(
            self.cps_value_frame,
            from_=1,
            to=20,
            number_of_steps=19,
            command=self.update_cps_value
        )
        self.cps_slider.pack(side="left", padx=5, fill="x", expand=True)
        self.cps_slider.set(self.auto_clicker_cps)

        # CPS Label
        self.cps_label = ctk.CTkLabel(
            self.cps_value_frame,
            text="10"
        )
        self.cps_label.pack(side="right")

        # Double Click Switch
        self.double_click_switch = ctk.CTkSwitch(
            self.combat_frame,
            text="Double Click",
            width=120,
            command=lambda: self.ui_toggle_double_click()
        )
        self.double_click_switch.pack(pady=5, padx=20)

        # Reach Switch
        self.reach_switch = ctk.CTkSwitch(
            self.combat_frame,
            text="Reach",
            width=120,
            command=lambda: self.ui_toggle_reach()
        )
        self.reach_switch.pack(pady=5, padx=20)
        self.reach_switch.bind('<Button-3>', self.toggle_reach_slider)

        # Reach Value Frame
        self.reach_value_frame = ctk.CTkFrame(self.combat_frame)

        # Reach Slider
        self.reach_slider = ctk.CTkSlider(
            self.reach_value_frame,
            from_=3,
            to=7,
            number_of_steps=40,  # 0.1 increments
            command=self.update_reach_value
        )
        self.reach_slider.pack(side="left", padx=5, fill="x", expand=True)
        self.reach_slider.set(self.reach_value)

        # Reach Value Label
        self.reach_value_label = ctk.CTkLabel(
            self.reach_value_frame,
            text="3.0"
        )
        self.reach_value_label.pack(side="right")

        # S-Tap Switch
        self.s_tap_switch = ctk.CTkSwitch(
            self.combat_frame,
            text="S-Tap",
            width=120,
            command=lambda: self.ui_toggle_s_tap()
        )
        self.s_tap_switch.pack(pady=5, padx=20)

        # Apply loaded color theme
        self.apply_color_theme()

        # Initialize connection on startup
        self.root.after(1000, self.initialize_connection)

        # Download sounds on startup (in background)
        if self.sounds_enabled:
            threading.Thread(target=self.download_sounds, daemon=True).start()

        # Update switch colors after all switches are created
        self.root.after(100, self.update_all_switch_colors)

        # Save config when application closes
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_settings_page(self):
        """Create the settings page"""
        self.settings_page = ctk.CTkFrame(
            self.content_container,
            fg_color=self.colors["frame_bg"],
            corner_radius=10
        )

        # Create tabview for settings categories
        tabview = ctk.CTkTabview(self.settings_page)
        tabview.pack(fill="both", expand=True, padx=20, pady=20)

        # Create tabs
        tab_appearance = tabview.add("Appearance")
        tab_hotkeys = tabview.add("Hotkeys")
        tab_audio = tabview.add("Audio")

        # === Appearance Tab ===
        # Appearance mode setting
        appearance_frame = ctk.CTkFrame(tab_appearance)
        appearance_frame.pack(pady=10, fill="x")

        appearance_label = ctk.CTkLabel(
            appearance_frame,
            text="Appearance Mode:",
            font=("Roboto", 14)
        )
        appearance_label.pack(pady=5, side="left", padx=10)

        appearance_option = ctk.CTkOptionMenu(
            appearance_frame,
            values=["System", "Dark", "Light"],
            command=self.change_appearance_mode
        )
        appearance_option.pack(pady=5, side="right", padx=10)

        # Color theme setting
        color_frame = ctk.CTkFrame(tab_appearance)
        color_frame.pack(pady=10, fill="x")

        color_label = ctk.CTkLabel(
            color_frame,
            text="Color Theme:",
            font=("Roboto", 14)
        )
        color_label.pack(pady=5, side="left", padx=10)

        color_option = ctk.CTkOptionMenu(
            color_frame,
            values=list(self.color_themes.keys()),
            command=self.change_color_theme
        )
        color_option.pack(pady=5, side="right", padx=10)
        color_option.set(self.current_theme)

        # === Hotkeys Tab ===
        # Create scrollable frame for hotkeys
        hotkeys_scrollable_frame = ctk.CTkScrollableFrame(tab_hotkeys)
        hotkeys_scrollable_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Movement modules
        movement_label = ctk.CTkLabel(
            hotkeys_scrollable_frame,
            text="Movement",
            font=("Roboto", 14, "bold")
        )
        movement_label.pack(pady=(10, 5), padx=10, anchor="w")

        # Add hotkey settings for each module
        self.create_hotkey_setting(hotkeys_scrollable_frame, "air_jump", "AirJump")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "fly", "Fly")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "fast_ladder", "FastLadder")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "phase", "Phase")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "high_jump", "HighJump")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "semi_scaffold", "Semi-Scaffold")

        # Player modules
        player_label = ctk.CTkLabel(
            hotkeys_scrollable_frame,
            text="Player",
            font=("Roboto", 14, "bold")
        )
        player_label.pack(pady=(10, 5), padx=10, anchor="w")

        self.create_hotkey_setting(hotkeys_scrollable_frame, "no_web", "NoWeb")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "antikb", "AntiKb")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "fast_place", "FastPlace")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "auto_sprint", "AutoSprint")

        # Combat modules
        combat_label = ctk.CTkLabel(
            hotkeys_scrollable_frame,
            text="Combat",
            font=("Roboto", 14, "bold")
        )
        combat_label.pack(pady=(10, 5), padx=10, anchor="w")

        self.create_hotkey_setting(hotkeys_scrollable_frame, "auto_clicker", "AutoClicker")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "double_click", "DoubleClick")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "rapid_hit", "Rapid Hit")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "reach", "Reach")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "s_tap", "S-Tap")

        # Visuals modules
        visuals_label = ctk.CTkLabel(
            hotkeys_scrollable_frame,
            text="Visuals",
            font=("Roboto", 14, "bold")
        )
        visuals_label.pack(pady=(10, 5), padx=10, anchor="w")

        self.create_hotkey_setting(hotkeys_scrollable_frame, "zoom", "Zoom")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "see_entities", "See Entities")
        self.create_hotkey_setting(hotkeys_scrollable_frame, "no_hurtcam", "No HurtCam")

        # === Audio Tab ===
        # Sound settings frame
        sound_frame = ctk.CTkFrame(tab_audio)
        sound_frame.pack(pady=10, fill="x")

        sound_label = ctk.CTkLabel(
            sound_frame,
            text="Enable/Disable Sounds:",
            font=("Roboto", 14)
        )
        sound_label.pack(pady=5, side="left", padx=10)

        # Sound toggle switch
        self.sound_switch = ctk.CTkSwitch(
            sound_frame,
            text="Enable Sounds",
            command=self.toggle_sounds
        )
        self.sound_switch.pack(pady=5, side="right", padx=10)

        # Set initial state based on config
        if self.sounds_enabled:
            self.sound_switch.select()
        else:
            self.sound_switch.deselect()

        # Buttons frame at the bottom
        buttons_frame = ctk.CTkFrame(self.settings_page)
        buttons_frame.pack(pady=10, fill="x", padx=20)

        # Reset Button
        reset_button = ctk.CTkButton(
            buttons_frame,
            text="Reset to Default",
            command=self.reset_to_default,
            fg_color="#D22B2B",
            hover_color="#AA0000"
        )
        reset_button.pack(pady=10, side="left", padx=10, fill="x", expand=True)

        # Save button
        save_button = ctk.CTkButton(
            buttons_frame,
            text="💾 Save Settings",
            command=self.save_settings_and_back
        )
        save_button.pack(pady=10, side="left", padx=10, fill="x", expand=True)

    def show_main_page(self):
        """Show the main client page"""
        self.current_page = "main"
        self.settings_page.pack_forget()
        self.main_page.pack(fill="both", expand=True)

        # Update header
        self.title_label.configure(text="FUSION CLIENT")
        self.back_button.pack_forget()
        self.settings_button.pack(pady=15, side="right", padx=20)

    def show_settings_page(self):
        """Show the settings page"""
        self.current_page = "settings"
        self.main_page.pack_forget()
        self.settings_page.pack(fill="both", expand=True)

        # Update header
        self.title_label.configure(text="⚙️ SETTINGS")
        self.settings_button.pack_forget()

        # Update back button colors and show it
        self.back_button.configure(
            fg_color=self.colors["frame_bg"],
            hover_color=self.colors["accent"],
            text_color=self.colors["text"]
        )
        self.back_button.pack(side="left", padx=20, pady=17)

    def save_settings_and_back(self):
        """Save settings and go back to main page"""
        # Save config
        self.save_config()

        # Restart hotkey listeners with new settings
        self.stop_hotkey_listeners()
        self.start_hotkey_listeners()

        # Go back to main page
        self.show_main_page()

    def configure_styles(self):
        """Konfiguriert die benutzerdefinierten Styles für die UI-Elemente"""
        # Switch Style
        style = ttk.Style()
        style.configure(
            "Custom.TSwitch",
            background=self.colors["switch_off"],
            troughcolor=self.colors["switch_off"],
            indicatorcolor=self.colors["switch_on"]
        )

        # Button Style
        style.configure(
            "Custom.TButton",
            background=self.colors["accent"],
            foreground=self.colors["text"],
            padding=10,
            font=("Segoe UI", 12)
        )

        # Label Style
        style.configure(
            "Custom.TLabel",
            background=self.colors["bg"],
            foreground=self.colors["text"],
            font=("Segoe UI", 12)
        )

        # Frame Style
        style.configure(
            "Custom.TFrame",
            background=self.colors["bg"]
        )

        # Slider Style
        style.configure(
            "Custom.Horizontal.TScale",
            background=self.colors["bg"],
            troughcolor=self.colors["frame_bg"],
            sliderthickness=20
        )

    def load_window_icon(self):
        """Load window icon from embedded Base64 data"""
        try:
            # Decode Base64 icon data
            icon_data = base64.b64decode(FUSION_ICON_BASE64)
            icon_image = Image.open(io.BytesIO(icon_data))

            # Convert to RGBA and resize
            icon_image = icon_image.convert("RGBA")
            icon_image = icon_image.resize((32, 32), Image.Resampling.LANCZOS)

            # Save as temporary ICO file
            icon_image.save("fusion_temp.ico", format="ICO")

            # Set window icon using iconbitmap
            self.root.iconbitmap("fusion_temp.ico")

            # Clean up temp file after a short delay
            self.root.after(1000, self.cleanup_temp_icon)

        except Exception as e:
            print(f"Could not load embedded icon: {e}")
            # Fallback: try to load from Fusion.jpg if it exists
            try:
                if os.path.exists("Fusion.jpg"):
                    icon_image = Image.open("Fusion.jpg")
                    icon_image = icon_image.convert("RGBA")
                    icon_image = icon_image.resize((32, 32), Image.Resampling.LANCZOS)
                    icon_image.save("fusion_temp.ico", format="ICO")
                    self.root.iconbitmap("fusion_temp.ico")
                    self.root.after(1000, self.cleanup_temp_icon)
            except Exception as e2:
                pass  # Ignore fallback errors

    def cleanup_temp_icon(self):
        """Clean up temporary icon file"""
        try:
            if os.path.exists("fusion_temp.ico"):
                os.remove("fusion_temp.ico")
        except Exception as e:
            pass  # Ignore cleanup errors

    def toggle_value_slider(self, event=None):
        try:
            if self.fast_ladder_value_frame.winfo_manager():
                self.fast_ladder_value_frame.pack_forget()
            else:
                self.fast_ladder_value_frame.pack(fill="x", pady=5, after=self.fast_ladder_switch)
                self.fast_ladder_slider.set(self.fast_ladder_value)
        except Exception as e:
            print(f"Error in toggle_value_slider: {e}")

    def toggle_high_jump_slider(self, event=None):
        try:
            if self.high_jump_value_frame.winfo_manager():
                self.high_jump_value_frame.pack_forget()
            else:
                self.high_jump_value_frame.pack(fill="x", pady=5, after=self.high_jump_switch)
                self.high_jump_slider.set(self.high_jump_value)
        except Exception as e:
            print(f"Error in toggle_high_jump_slider: {e}")

    def toggle_zoom_hold_frame(self, event=None):
        if hasattr(self, 'zoom_hold_frame'):
            if self.zoom_hold_frame.winfo_manager():
                self.zoom_hold_frame.pack_forget()
            else:
                self.zoom_hold_frame.pack(fill="x", pady=5, after=self.zoom_switch)

    def toggle_reach_slider(self, event=None):
        try:
            if self.reach_value_frame.winfo_manager():
                self.reach_value_frame.pack_forget()
            else:
                self.reach_value_frame.pack(fill="x", pady=5, after=self.reach_switch)
                self.reach_slider.set(self.reach_value)
        except Exception as e:
            print(f"Error in toggle_reach_slider: {e}")



    def find_process_by_name(self, process_name):
        for proc in psutil.process_iter(['name', 'pid']):
            try:
                if process_name.lower() in proc.info['name'].lower():
                    return proc.info['pid']
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return None

    def initialize_connection(self):
        try:
            minecraft_pid = self.find_process_by_name("Minecraft.Windows.exe")

            if not minecraft_pid:
                # self.status_label.configure(text="Status: Minecraft not running")
                self.root.after(2000, self.initialize_connection)
                return

            try:
                self.pm = Pymem("Minecraft.Windows.exe")
            except Exception as e:
                # self.status_label.configure(text="Status: Cannot attach to process")
                self.root.after(2000, self.initialize_connection)
                return

            # self.status_label.configure(text="Status: Scanning for patterns...")

            # Definiere alle Patterns für parallele Suche
            patterns = {
                "AirJump": self.original_bytes,
                "FastLadder": self.original_ladder_bytes,
                "Phase": self.original_phase_bytes,
                "NoWeb": self.original_no_web_bytes,
                "HighJump": self.original_high_jump_bytes,
                "Fly": self.original_fly_bytes,
                "AntiKb1": self.original_antikb_bytes1,
                "AntiKb2": self.original_antikb_bytes2,
                "Semi-Scaffold1": self.original_scaffold_bytes1,
                "Semi-Scaffold2": self.original_scaffold_bytes2,
                "Rapid Hit": self.original_rapid_hit_bytes,
                "Zoom": self.original_zoom_bytes,
                "See Entities": self.original_see_entities_bytes,
                "No HurtCam": self.original_no_hurtcam_bytes,
                "FastPlace": self.original_fast_place_bytes,
                "AutoSprint": self.original_auto_sprint_bytes
            }

            # Führe parallele Pattern-Suche durch
            results = self.parallel_pattern_search(patterns)

            # Weise die gefundenen Adressen zu
            self.air_jump_address = results.get("AirJump")
            self.fast_ladder_address = results.get("FastLadder")
            self.phase_address = results.get("Phase")
            self.no_web_address = results.get("NoWeb")
            self.high_jump_address = results.get("HighJump")
            self.fly_address = results.get("Fly")
            self.antikb_address1 = results.get("AntiKb1")
            self.antikb_address2 = results.get("AntiKb2")
            self.scaffold_address1 = results.get("Semi-Scaffold1")
            self.scaffold_address2 = results.get("Semi-Scaffold2")
            self.rapid_hit_address = results.get("Rapid Hit")
            self.zoom_address = results.get("Zoom")
            self.see_entities_address = results.get("See Entities")
            self.no_hurtcam_address = results.get("No HurtCam")
            self.fast_place_address = results.get("FastPlace")
            self.auto_sprint_address = results.get("AutoSprint")

            # Set reach address using static offset
            try:
                self.reach_address = self.pm.base_address + self.reach_offset
            except Exception as e:
                print(f"Failed to set reach address: {e}")
                self.reach_address = None


            # Start hotkey listeners
            self.start_hotkey_listeners()

            # Restore saved module states after connection is established
            self.root.after(500, self.restore_module_states)

            self.status_label.configure(text="")

        except Exception as e:
            # self.status_label.configure(text=f"Status: Initialization Error - {str(e)[:50]}")
            self.root.after(2000, self.initialize_connection)

    def float_to_bytes(self, value):
        return struct.pack('<f', value)

    def ui_toggle_air_jump(self):
        self._toggle_source = 'ui'
        self.toggle_air_jump()

    def ui_toggle_fly(self):
        self._toggle_source = 'ui'
        self.toggle_fly()

    def ui_toggle_fast_ladder(self):
        self._toggle_source = 'ui'
        self.toggle_fast_ladder()

    def ui_toggle_phase(self):
        self._toggle_source = 'ui'
        self.toggle_phase()

    def ui_toggle_no_web(self):
        self._toggle_source = 'ui'
        self.toggle_no_web()

    def ui_toggle_high_jump(self):
        self._toggle_source = 'ui'
        self.toggle_high_jump()

    def ui_toggle_antikb(self):
        self._toggle_source = 'ui'
        self.toggle_antikb()

    def ui_toggle_semi_scaffold(self):
        self._toggle_source = 'ui'
        self.toggle_semi_scaffold()

    def ui_toggle_auto_clicker(self):
        self._toggle_source = 'ui'
        self.toggle_auto_clicker()

    def ui_toggle_rapid_hit(self):
        self._toggle_source = 'ui'
        self.toggle_rapid_hit()

    def ui_toggle_zoom(self):
        self._toggle_source = 'ui'
        self.toggle_zoom()

    def ui_toggle_see_entities(self):
        self._toggle_source = 'ui'
        self.toggle_see_entities()

    def ui_toggle_no_hurtcam(self):
        self._toggle_source = 'ui'
        self.toggle_no_hurtcam()

    def ui_toggle_reach(self):
        self._toggle_source = 'ui'
        self.toggle_reach()

    def ui_toggle_s_tap(self):
        self._toggle_source = 'ui'
        self.toggle_s_tap()

    def ui_toggle_auto_sprint(self):
        self._toggle_source = 'ui'
        self.toggle_auto_sprint()



    def toggle_air_jump(self):
        if not self.pm or not self.air_jump_address:
            # self.status_label.configure(text="Status: AirJump nicht verfügbar - Minecraft nicht verbunden")
            self.air_jump_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.air_jump_switch.get()
                if current_state:
                    self.air_jump_switch.deselect()
                else:
                    self.air_jump_switch.select()

            # Now apply the changes based on the switch state
            if self.air_jump_switch.get():
                self.pm.write_bytes(self.air_jump_address, self.patched_bytes, len(self.patched_bytes))
                self.is_air_jump_enabled = True
                self.play_sound("enable")
                # self.status_label.configure(text="Status: AirJump aktiviert")
            else:
                self.pm.write_bytes(self.air_jump_address, self.original_bytes, len(self.original_bytes))
                self.is_air_jump_enabled = False
                self.play_sound("disable")
                # self.status_label.configure(text="Status: AirJump deaktiviert")

            # Save config to persist state
            self.save_config()
            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text=f"Status: AirJump Fehler - {str(e)[:50]}")
            self.air_jump_switch.deselect()
            self._toggle_source = None  # Reset toggle source

    def update_fast_ladder_value(self, value):
        self.fast_ladder_value = float(value)
        self.fast_ladder_value_label.configure(text=f"{self.fast_ladder_value:.1f}")
        if self.is_fast_ladder_enabled:
            self.apply_fast_ladder()
        # Save config when value changes
        self.save_config()

    def apply_fast_ladder(self):
        if not self.pm or not self.fast_ladder_address:
            return

        try:
            # Create the instruction bytes
            mov_instruction = bytes([0xC7, 0x47, 0x1C])  # mov [rdi+1C],
            value_bytes = self.float_to_bytes(self.fast_ladder_value)
            patched_bytes = mov_instruction + value_bytes

            # Write the new value
            self.pm.write_bytes(self.fast_ladder_address, patched_bytes, len(patched_bytes))
        except Exception as e:
            # self.status_label.configure(text="Status: FastLadder Patching Error")
            self.fast_ladder_switch.deselect()

    def toggle_fast_ladder(self):
        if not self.pm or not self.fast_ladder_address:
            self.fast_ladder_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.fast_ladder_switch.get()
                if current_state:
                    self.fast_ladder_switch.deselect()
                else:
                    self.fast_ladder_switch.select()

            if self.fast_ladder_switch.get():
                self.is_fast_ladder_enabled = True
                self.apply_fast_ladder()
                self.play_sound("enable")
            else:
                self.is_fast_ladder_enabled = False
                self.pm.write_bytes(self.fast_ladder_address, self.original_ladder_bytes, len(self.original_ladder_bytes))
                self.play_sound("disable")

            # Save config to persist state
            self.save_config()
            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text="Status: FastLadder Patching Error")
            self.fast_ladder_switch.deselect()
            self._toggle_source = None  # Reset toggle source

    def toggle_phase(self):
        if not self.pm or not self.phase_address:
            self.phase_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.phase_switch.get()
                if current_state:
                    self.phase_switch.deselect()
                else:
                    self.phase_switch.select()

            if self.phase_switch.get():
                # Write the patched bytes (NOPs for first 5 bytes)
                self.pm.write_bytes(self.phase_address, self.phase_patched_bytes, len(self.phase_patched_bytes))
                self.is_phase_enabled = True
                self.play_sound("enable")
            else:
                # Restore the original bytes
                self.pm.write_bytes(self.phase_address, self.original_phase_bytes, len(self.original_phase_bytes))
                self.is_phase_enabled = False
                self.play_sound("disable")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text=f"Status: Phase Patching Error - {str(e)[:50]}")
            self.phase_switch.deselect()
            self._toggle_source = None  # Reset toggle source

    def toggle_no_web(self):
        if not self.pm or not self.no_web_address:
            self.no_web_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.no_web_switch.get()
                if current_state:
                    self.no_web_switch.deselect()
                else:
                    self.no_web_switch.select()

            if self.no_web_switch.get():
                # Write NOPs for the first movss instruction only
                self.pm.write_bytes(self.no_web_address, self.no_web_patched_bytes, len(self.no_web_patched_bytes))
                self.is_no_web_enabled = True
                self.play_sound("enable")
            else:
                # Restore only the first movss instruction
                restore_bytes = bytes([0xF3, 0x0F, 0x10, 0x2F])
                self.pm.write_bytes(self.no_web_address, restore_bytes, len(restore_bytes))
                self.is_no_web_enabled = False
                self.play_sound("disable")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text="Status: NoWeb Patching Error")
            self.no_web_switch.deselect()
            self._toggle_source = None  # Reset toggle source

    def update_high_jump_value(self, value):
        self.high_jump_value = float(value)
        self.high_jump_value_label.configure(text=f"{self.high_jump_value:.1f}")
        if self.is_high_jump_enabled:
            self.apply_high_jump()
        # Save config when value changes
        self.save_config()

    def apply_high_jump(self):
        if not self.pm or not self.high_jump_address:
            return

        try:
            # Simple direct patching like FastLadder - just replace the mov instruction
            # Original: 89 01 41 F6 04 24 08 (mov [rcx],eax + test byte ptr [r12],08)
            # Patched: C7 01 XX XX XX XX 90 (mov [rcx],float_value + nop)
            mov_instruction = bytes([0xC7, 0x01])  # mov [rcx],immediate value
            value_bytes = self.float_to_bytes(self.high_jump_value)
            nop_byte = bytes([0x90])  # nop to fill the remaining byte

            patched_bytes = mov_instruction + value_bytes + nop_byte

            # Write the patched bytes directly
            self.pm.write_bytes(self.high_jump_address, patched_bytes, len(self.original_high_jump_bytes))

        except Exception as e:
            self.high_jump_switch.deselect()

    def toggle_high_jump(self):
        if not self.pm or not self.high_jump_address:
            self.high_jump_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.high_jump_switch.get()
                if current_state:
                    self.high_jump_switch.deselect()
                else:
                    self.high_jump_switch.select()

            if self.high_jump_switch.get():
                self.is_high_jump_enabled = True
                self.apply_high_jump()
                self.play_sound("enable")
            else:
                self.is_high_jump_enabled = False
                # Restore original bytes (simple like FastLadder)
                self.pm.write_bytes(self.high_jump_address, self.original_high_jump_bytes, len(self.original_high_jump_bytes))
                self.play_sound("disable")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            self.high_jump_switch.deselect()
            self.is_high_jump_enabled = False
            self._toggle_source = None  # Reset toggle source



    def toggle_fly(self):
        if not self.pm or not self.fly_address:
            self.fly_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.fly_switch.get()
                if current_state:
                    self.fly_switch.deselect()
                else:
                    self.fly_switch.select()

            # Now apply the changes based on the switch state
            if self.fly_switch.get():
                # Aktivierung: mov [rcx+04],1 + NOP
                self.pm.write_bytes(self.fly_address, self.fly_patched_bytes, len(self.fly_patched_bytes))
                self.is_fly_enabled = True
                self.play_sound("enable")
                # self.status_label.configure(text="Status: Fly aktiviert")
            else:
                # Deaktivierung: Stelle originale Bytes wieder her
                self.pm.write_bytes(self.fly_address, self.original_fly_bytes, len(self.original_fly_bytes))
                self.is_fly_enabled = False
                self.play_sound("disable")
                # self.status_label.configure(text="Status: Fly deaktiviert")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text=f"Status: Fly Fehler - {str(e)[:50]}")
            self.fly_switch.deselect()
            self._toggle_source = None  # Reset toggle source



    def toggle_antikb(self):
        # Check if Minecraft is connected
        if not self.pm:
            # self.status_label.configure(text="Status: AntiKb not available - Minecraft not connected")
            self.antikb_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        # Check if at least one pattern was found
        if not self.antikb_address1 and not self.antikb_address2:
            # self.status_label.configure(text="Status: AntiKb not available - No patterns found")
            self.antikb_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.antikb_switch.get()
                if current_state:
                    self.antikb_switch.deselect()
                else:
                    self.antikb_switch.select()

            if self.antikb_switch.get():
                # Activation: Patch available addresses
                if self.antikb_address1:
                    self.pm.write_bytes(self.antikb_address1, self.patched_antikb_bytes1, len(self.patched_antikb_bytes1))

                if self.antikb_address2:
                    self.pm.write_bytes(self.antikb_address2, self.patched_antikb_bytes2, len(self.patched_antikb_bytes2))

                self.is_antikb_enabled = True
                self.play_sound("enable")
                # self.status_label.configure(text="Status: AntiKb enabled")
            else:
                # Deactivation: Restore original bytes for available addresses
                if self.antikb_address1:
                    self.pm.write_bytes(self.antikb_address1, self.original_antikb_bytes1, len(self.original_antikb_bytes1))

                if self.antikb_address2:
                    self.pm.write_bytes(self.antikb_address2, self.original_antikb_bytes2, len(self.original_antikb_bytes2))

                self.is_antikb_enabled = False
                self.play_sound("disable")
                # self.status_label.configure(text="Status: AntiKb disabled")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text=f"Status: AntiKb Error - {str(e)[:50]}")
            self.antikb_switch.deselect()
            self.is_antikb_enabled = False
            self._toggle_source = None  # Reset toggle source

    def toggle_semi_scaffold(self):
        if not self.pm or not self.scaffold_address1 or not self.scaffold_address2:
            self.semi_scaffold_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.semi_scaffold_switch.get()
                if current_state:
                    self.semi_scaffold_switch.deselect()
                else:
                    self.semi_scaffold_switch.select()

            if self.semi_scaffold_switch.get():
                # Patch both addresses
                self.pm.write_bytes(self.scaffold_address1, self.patched_scaffold_bytes1, len(self.patched_scaffold_bytes1))
                self.pm.write_bytes(self.scaffold_address2, self.patched_scaffold_bytes2, len(self.patched_scaffold_bytes2))
                self.is_semi_scaffold_enabled = True
                self.play_sound("enable")
                # self.status_label.configure(text="Status: Semi-Scaffold enabled")
            else:
                # Restore both addresses
                self.pm.write_bytes(self.scaffold_address1, self.original_scaffold_bytes1, len(self.original_scaffold_bytes1))
                self.pm.write_bytes(self.scaffold_address2, self.original_scaffold_bytes2, len(self.original_scaffold_bytes2))
                self.is_semi_scaffold_enabled = False
                self.play_sound("disable")
                # self.status_label.configure(text="Status: Semi-Scaffold disabled")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text=f"Status: Semi-Scaffold Fehler - {str(e)[:50]}")
            self.semi_scaffold_switch.deselect()
            self._toggle_source = None  # Reset toggle source

    def toggle_rapid_hit(self):
        if not self.pm or not self.rapid_hit_address:
            self.rapid_hit_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.rapid_hit_switch.get()
                if current_state:
                    self.rapid_hit_switch.deselect()
                else:
                    self.rapid_hit_switch.select()

            if self.rapid_hit_switch.get():
                # Patch the address
                self.pm.write_bytes(self.rapid_hit_address, self.patched_rapid_hit_bytes, len(self.patched_rapid_hit_bytes))
                self.is_rapid_hit_enabled = True
                self.play_sound("enable")
                # self.status_label.configure(text="Status: Rapid Hit enabled")
            else:
                # Restore the address
                self.pm.write_bytes(self.rapid_hit_address, self.original_rapid_hit_bytes, len(self.original_rapid_hit_bytes))
                self.is_rapid_hit_enabled = False
                self.play_sound("disable")
                # self.status_label.configure(text="Status: Rapid Hit disabled")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text=f"Status: Rapid Hit Fehler - {str(e)[:50]}")
            self.rapid_hit_switch.deselect()
            self._toggle_source = None  # Reset toggle source

    def update_reach_value(self, value):
        self.reach_value = float(value)
        self.reach_value_label.configure(text=f"{self.reach_value:.1f}")
        if self.is_reach_enabled:
            self.apply_reach()
        # Save config when value changes
        self.save_config()

    def apply_reach(self):
        if not self.pm or not self.reach_address:
            return

        try:
            # Change memory protection to allow writing
            import ctypes
            from ctypes import wintypes

            # Constants for VirtualProtectEx
            PAGE_EXECUTE_READWRITE = 0x40

            kernel32 = ctypes.windll.kernel32
            old_protect = wintypes.DWORD()

            # Change protection to allow writing (use c_void_p for 64-bit addresses)
            success = kernel32.VirtualProtectEx(
                self.pm.process_handle,
                ctypes.c_void_p(self.reach_address),
                ctypes.c_size_t(4),  # size of float
                wintypes.DWORD(PAGE_EXECUTE_READWRITE),
                ctypes.byref(old_protect)
            )

            if not success:
                raise Exception(f"Failed to change memory protection. Error: {kernel32.GetLastError()}")

            # Write the new float value
            value_bytes = self.float_to_bytes(self.reach_value)
            self.pm.write_bytes(self.reach_address, value_bytes, len(value_bytes))

            # Restore original protection
            temp_protect = wintypes.DWORD()
            kernel32.VirtualProtectEx(
                self.pm.process_handle,
                ctypes.c_void_p(self.reach_address),
                ctypes.c_size_t(4),
                old_protect,
                ctypes.byref(temp_protect)
            )

        except Exception as e:
            print(f"Error applying reach: {e}")
            self.reach_switch.deselect()

    def toggle_reach(self):
        if not self.pm or not self.reach_address:
            self.reach_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.reach_switch.get()
                if current_state:
                    self.reach_switch.deselect()
                else:
                    self.reach_switch.select()

            if self.reach_switch.get():
                self.is_reach_enabled = True
                self.apply_reach()
                self.play_sound("enable")
            else:
                self.is_reach_enabled = False
                self.play_sound("disable")
                # Restore default value (3.0)
                try:
                    # Change memory protection to allow writing
                    import ctypes
                    from ctypes import wintypes

                    PAGE_EXECUTE_READWRITE = 0x40
                    kernel32 = ctypes.windll.kernel32
                    old_protect = wintypes.DWORD()

                    # Change protection
                    kernel32.VirtualProtectEx(
                        self.pm.process_handle,
                        ctypes.c_void_p(self.reach_address),
                        ctypes.c_size_t(4),
                        wintypes.DWORD(PAGE_EXECUTE_READWRITE),
                        ctypes.byref(old_protect)
                    )

                    # Write default value
                    default_value_bytes = self.float_to_bytes(3.0)
                    self.pm.write_bytes(self.reach_address, default_value_bytes, len(default_value_bytes))

                    # Restore protection
                    temp_protect = wintypes.DWORD()
                    kernel32.VirtualProtectEx(
                        self.pm.process_handle,
                        ctypes.c_void_p(self.reach_address),
                        ctypes.c_size_t(4),
                        old_protect,
                        ctypes.byref(temp_protect)
                    )

                except Exception as e:
                    print(f"Error restoring reach: {e}")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            print(f"Error in toggle_reach: {e}")
            self.reach_switch.deselect()
            self._toggle_source = None  # Reset toggle source

    def toggle_cps_slider(self, event=None):
        if hasattr(self, 'cps_value_frame'):
            if self.cps_value_frame.winfo_manager():
                self.cps_value_frame.pack_forget()
            else:
                self.cps_value_frame.pack(fill="x", pady=5, after=self.auto_clicker_switch)
                self.cps_slider.set(self.auto_clicker_cps)

    def update_cps_value(self, value):
        self.auto_clicker_cps = int(value)
        self.cps_label.configure(text=f"{self.auto_clicker_cps}")
        # Save config when value changes
        self.save_config()

    def auto_clicker_loop(self):
        last_click_time = time.time()
        while True:
            if not self.is_auto_clicker_enabled:
                break

            current_time = time.time()

            # Prüfe ob das Minecraft-Fenster aktiv ist
            try:
                # Hole das aktive Fenster
                hwnd = win32gui.GetForegroundWindow()
                # Hole den Fenstertitel
                window_title = win32gui.GetWindowText(hwnd)

                # Prüfe ob es das Minecraft-Fenster ist
                if "Minecraft" in window_title:
                    # Prüfe ob die linke Maustaste gedrückt ist (0x8000 = gedrückt)
                    if win32api.GetAsyncKeyState(0x01) & 0x8000:
                        if current_time - last_click_time >= (1.0 / self.auto_clicker_cps):
                            # Sende Klick-Events direkt
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                            last_click_time = current_time
            except Exception as e:
                print(f"Fehler beim Prüfen des Fensters: {e}")

            time.sleep(0.001)  # Minimale Pause zur CPU-Entlastung

    def toggle_auto_clicker(self):
        # Toggle the switch state if called via hotkey
        if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
            current_state = self.auto_clicker_switch.get()
            if current_state:
                self.auto_clicker_switch.deselect()
            else:
                self.auto_clicker_switch.select()

        if self.auto_clicker_switch.get():
            self.is_auto_clicker_enabled = True

            # Starte neuen Thread
            if not self.auto_clicker_thread or not self.auto_clicker_thread.is_alive():
                self.auto_clicker_thread = threading.Thread(target=self.auto_clicker_loop, daemon=True)
                self.auto_clicker_thread.start()
                self.play_sound("enable")
                # self.status_label.configure(text=f"Status: AutoClicker enabled - {self.auto_clicker_cps} CPS")
        else:
            self.is_auto_clicker_enabled = False
            self.play_sound("disable")
            # self.status_label.configure(text="Status: AutoClicker disabled")

        self._toggle_source = None  # Reset toggle source

    def s_tap_loop(self):
        """S-Tap loop - drückt 'S' bei jedem Linksklick (Auto S-Tap)"""
        last_button_state = False  # Track the previous button state

        while True:
            if not self.is_s_tap_enabled:
                break

            try:
                # Prüfe ob das Minecraft-Fenster aktiv ist
                hwnd = win32gui.GetForegroundWindow()
                window_title = win32gui.GetWindowText(hwnd)

                # Prüfe ob es das Minecraft-Fenster ist
                if "Minecraft" in window_title:
                    # Get current left mouse button state
                    current_button_state = win32api.GetAsyncKeyState(0x01) & 0x8000

                    # Check for button press (transition from not pressed to pressed)
                    if current_button_state and not last_button_state:
                        # Linksklick erkannt - S-TASTE LÄNGER GEDRÜCKT HALTEN
                        win32api.keybd_event(0x53, 0, 0, 0)  # S key down
                        time.sleep(0.15)  # Länger halten (150ms) - "fett drücken"
                        win32api.keybd_event(0x53, 0, win32con.KEYEVENTF_KEYUP, 0)  # S key up

                    # Update last button state
                    last_button_state = current_button_state
                    time.sleep(0.01)  # Kurze Pause für CPU-Entlastung
                else:
                    time.sleep(0.1)  # Längere Pause wenn Minecraft nicht aktiv ist
                    last_button_state = False  # Reset state when not in Minecraft
            except Exception as e:
                print(f"Fehler im S-Tap Loop: {e}")
                time.sleep(0.1)

    def toggle_s_tap(self):
        # Toggle the switch state if called via hotkey
        if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
            current_state = self.s_tap_switch.get()
            if current_state:
                self.s_tap_switch.deselect()
            else:
                self.s_tap_switch.select()

        if self.s_tap_switch.get():
            self.is_s_tap_enabled = True

            # Starte neuen Thread
            if not self.s_tap_thread or not self.s_tap_thread.is_alive():
                self.s_tap_thread = threading.Thread(target=self.s_tap_loop, daemon=True)
                self.s_tap_thread.start()
                self.play_sound("enable")
        else:
            self.is_s_tap_enabled = False
            self.play_sound("disable")

        self._toggle_source = None  # Reset toggle source

    def toggle_auto_sprint(self):
        if not self.pm or not self.auto_sprint_address:
            self.auto_sprint_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.auto_sprint_switch.get()
                if current_state:
                    self.auto_sprint_switch.deselect()
                else:
                    self.auto_sprint_switch.select()

            # Apply changes based on the switch state
            if self.auto_sprint_switch.get():
                # Activation: Write patched bytes (mov [r9+08],1)
                self.pm.write_bytes(self.auto_sprint_address, self.patched_auto_sprint_bytes, len(self.patched_auto_sprint_bytes))
                self.is_auto_sprint_enabled = True
                self.play_sound("enable")
                # self.status_label.configure(text="Status: AutoSprint enabled")
            else:
                # Deactivation: Restore original bytes
                self.pm.write_bytes(self.auto_sprint_address, self.original_auto_sprint_bytes, len(self.original_auto_sprint_bytes))
                self.is_auto_sprint_enabled = False
                self.play_sound("disable")
                # self.status_label.configure(text="Status: AutoSprint disabled")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text=f"Status: AutoSprint Error - {str(e)}")
            self.auto_sprint_switch.deselect()
            self._toggle_source = None  # Reset toggle source

    def open_settings(self):
        """Open settings in the same window"""
        self.show_settings_page()

    def open_settings_old_commented_out(self):
        """OLD SETTINGS CODE - COMMENTED OUT"""
        pass
        # OLD CODE COMMENTED OUT
        """
            appearance_frame,
            text="Appearance Mode:",
            font=("Roboto", 14)
        )
        appearance_label.pack(pady=5, side="left", padx=10)

        appearance_option = ctk.CTkOptionMenu(
            appearance_frame,
            values=["System", "Dark", "Light"],
            command=self.change_appearance_mode
        )
        appearance_option.pack(pady=5, side="right", padx=10)
        appearance_option.set("Dark")

        # Color theme setting
        color_theme_frame = ctk.CTkFrame(tab_appearance)
        color_theme_frame.pack(pady=10, fill="x")

        color_theme_label = ctk.CTkLabel(
            color_theme_frame,
            text="Color Theme:",
            font=("Roboto", 14)
        )
        color_theme_label.pack(pady=5, side="left", padx=10)

        self.color_theme_option = ctk.CTkOptionMenu(
            color_theme_frame,
            values=list(self.color_themes.keys()),
            command=self.change_color_theme
        )
        self.color_theme_option.pack(pady=5, side="right", padx=10)
        self.color_theme_option.set(self.current_theme)

        # === Hotkeys Tab ===
        # Scrollable frame for hotkeys
        hotkeys_scrollable_frame = ctk.CTkScrollableFrame(tab_hotkeys)
        hotkeys_scrollable_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Hotkey instructions
        hotkey_instructions = ctk.CTkLabel(
            hotkeys_scrollable_frame,
            text="Click on a button and press a key to set a hotkey.",
            font=("Roboto", 12),
            wraplength=400
        )
        hotkey_instructions.pack(pady=5, padx=10, fill="x")

        # Create hotkey setting frames for each module

        # Movement modules
        movement_label = ctk.CTkLabel(
            hotkeys_scrollable_frame,
            text="Movement",
            font=("Roboto", 14, "bold")
        )
        movement_label.pack(pady=(10, 5), padx=10, anchor="w")

        # AirJump hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "air_jump", "AirJump")

        # Fly hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "fly", "Fly")

        # FastLadder hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "fast_ladder", "FastLadder")

        # Phase hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "phase", "Phase")

        # HighJump hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "high_jump", "HighJump")

        # Semi-Scaffold hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "semi_scaffold", "Semi-Scaffold")

        # Player modules
        player_label = ctk.CTkLabel(
            hotkeys_scrollable_frame,
            text="Player",
            font=("Roboto", 14, "bold")
        )
        player_label.pack(pady=(10, 5), padx=10, anchor="w")

        # NoWeb hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "no_web", "NoWeb")

        # AntiKb hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "antikb", "AntiKb")

        # FastPlace hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "fast_place", "FastPlace")

        # Combat modules
        combat_label = ctk.CTkLabel(
            hotkeys_scrollable_frame,
            text="Combat",
            font=("Roboto", 14, "bold")
        )
        combat_label.pack(pady=(10, 5), padx=10, anchor="w")

        # AutoClicker hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "auto_clicker", "AutoClicker")

        # DoubleClick hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "double_click", "DoubleClick")

        # Rapid Hit hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "rapid_hit", "Rapid Hit")

        # Reach hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "reach", "Reach")

        # Visuals modules
        visuals_label = ctk.CTkLabel(
            hotkeys_scrollable_frame,
            text="Visuals",
            font=("Roboto", 14, "bold")
        )
        visuals_label.pack(pady=(10, 5), padx=10, anchor="w")

        # Zoom hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "zoom", "Zoom")

        # See Entities hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "see_entities", "See Entities")

        # No HurtCam hotkey
        self.create_hotkey_setting(hotkeys_scrollable_frame, "no_hurtcam", "No HurtCam")





        # Buttons frame at the bottom
        buttons_frame = ctk.CTkFrame(settings_frame)
        buttons_frame.pack(pady=10, fill="x")

        # Reset Button
        reset_button = ctk.CTkButton(
            buttons_frame,
            text="Reset to Default",
            command=self.reset_to_default,
            fg_color="#D22B2B",  # Rote Farbe für Reset-Button
            hover_color="#AA0000"
        )
        reset_button.pack(pady=10, side="left", padx=10, fill="x", expand=True)

        # Save button
        save_button = ctk.CTkButton(
            buttons_frame,
            text="Save",
            command=lambda: self.save_settings_and_close(settings_window)
        )
        save_button.pack(pady=10, side="left", padx=10, fill="x", expand=True)

        # Close button
        close_button = ctk.CTkButton(
            buttons_frame,
            text="Close",
            command=settings_window.destroy
        )
        close_button.pack(pady=10, side="left", padx=10, fill="x", expand=True)
        """

    def create_hotkey_setting(self, parent, hotkey_id, display_name):
        frame = ctk.CTkFrame(parent)
        frame.pack(pady=5, padx=10, fill="x")

        label = ctk.CTkLabel(
            frame,
            text=f"{display_name}:",
            font=("Roboto", 12)
        )
        label.pack(pady=5, side="left", padx=10)

        current_hotkey = self.hotkeys.get(hotkey_id, "")

        button = ctk.CTkButton(
            frame,
            text=current_hotkey if current_hotkey else "Click to set",
            command=lambda: self.start_hotkey_capture(hotkey_id, button)
        )
        button.pack(pady=5, side="right", padx=10)

        # Store button reference
        self.hotkey_buttons[hotkey_id] = button

        # Clear button
        clear_button = ctk.CTkButton(
            frame,
            text="Clear",
            width=60,
            command=lambda: self.clear_hotkey(hotkey_id, button)
        )
        clear_button.pack(pady=5, side="right", padx=5)

    def start_hotkey_capture(self, hotkey_id, button):
        button.configure(text="Press a key...")

        # Create a listener for the next key press
        def on_key_press(event):
            key_name = event.name
            if key_name == "escape":
                # Cancel if Escape is pressed
                button.configure(text=self.hotkeys.get(hotkey_id, "") or "Click to set")
            else:
                # Set the new hotkey
                self.hotkeys[hotkey_id] = key_name
                button.configure(text=key_name)

            # Remove the listener
            keyboard.unhook(hook_id)

        # Hook the keyboard event
        hook_id = keyboard.on_press(on_key_press)

    def clear_hotkey(self, hotkey_id, button):
        self.hotkeys[hotkey_id] = ""
        button.configure(text="Click to set")

    def save_settings_and_close(self, window):
        # Save config
        self.save_config()

        # Restart hotkey listeners with new settings
        self.stop_hotkey_listeners()
        self.start_hotkey_listeners()

        # Close window
        window.destroy()

        # self.status_label.configure(text="Status: Settings saved")

    def reset_to_default(self):
        # Reset appearance
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        # Reset all frames to default colors
        default_frame_color = None  # Use default theme color
        for frame in [self.movement_frame, self.combat_frame, self.player_frame, self.visuals_frame]:
            frame.configure(fg_color=default_frame_color)

        # Reset all text labels to default colors
        for label in [self.movement_label, self.combat_label, self.player_label, self.visuals_label]:
            label.configure(text_color=None)  # Use default theme color

        # self.status_label.configure(text="Status: Settings reset to default")

    def change_appearance_mode(self, new_appearance_mode):
        ctk.set_appearance_mode(new_appearance_mode.lower())

    def change_color_theme(self, new_theme):
        """Change the color theme and update all UI elements"""
        self.current_theme = new_theme
        self.colors = self.color_themes[new_theme]

        # Update all UI elements with new colors
        self.apply_color_theme()

    def apply_color_theme(self):
        """Apply the current color theme to all UI elements"""
        try:
            # Update main frames
            self.main_frame.configure(fg_color=self.colors["bg"])
            self.header_frame.configure(fg_color=self.colors["frame_bg"])
            self.categories_container.configure(fg_color=self.colors["frame_bg"])

            # Update category frames
            self.movement_frame.configure(fg_color=self.colors["bg"])
            self.combat_frame.configure(fg_color=self.colors["bg"])
            self.player_frame.configure(fg_color=self.colors["bg"])
            self.visuals_frame.configure(fg_color=self.colors["bg"])

            # Update title and category labels
            self.title_label.configure(text_color=self.colors["accent"])
            self.movement_label.configure(text_color=self.colors["accent"])
            self.combat_label.configure(text_color=self.colors["accent"])
            self.player_label.configure(text_color=self.colors["accent"])
            self.visuals_label.configure(text_color=self.colors["accent"])

            # Update status label
            self.status_label.configure(text_color=self.colors["text_secondary"])

            # Update settings button
            self.settings_button.configure(
                fg_color=self.colors["frame_bg"],
                hover_color=self.colors["accent"]
            )

            # Update back button
            if hasattr(self, 'back_button'):
                self.back_button.configure(
                    fg_color=self.colors["frame_bg"],
                    hover_color=self.colors["accent"],
                    text_color=self.colors["text"]
                )

            # Update all switches to use new accent color (with safety checks)
            switch_names = [
                'air_jump_switch', 'fast_ladder_switch', 'phase_switch',
                'no_web_switch', 'high_jump_switch', 'fly_switch',
                'antikb_switch', 'semi_scaffold_switch', 'rapid_hit_switch',
                'auto_clicker_switch', 'fast_place_switch', 'double_click_switch',
                'zoom_switch', 'zoom_hold_switch', 'see_entities_switch', 'no_hurtcam_switch',
                'reach_switch', 's_tap_switch', 'auto_sprint_switch'
            ]

            for switch_name in switch_names:
                if hasattr(self, switch_name):
                    switch = getattr(self, switch_name)
                    if hasattr(switch, 'configure'):
                        switch.configure(
                            progress_color=self.colors["switch_on"],
                            button_color=self.colors["switch_on"]
                        )

        except Exception as e:
            print(f"Error applying color theme: {e}")

    def update_all_switch_colors(self):
        """Update all switch colors - called after all switches are created"""
        try:
            switch_names = [
                'air_jump_switch', 'fast_ladder_switch', 'phase_switch',
                'no_web_switch', 'high_jump_switch', 'fly_switch',
                'antikb_switch', 'semi_scaffold_switch', 'rapid_hit_switch',
                'auto_clicker_switch', 'fast_place_switch', 'double_click_switch',
                'zoom_switch', 'zoom_hold_switch', 'see_entities_switch', 'no_hurtcam_switch',
                'reach_switch', 's_tap_switch', 'auto_sprint_switch'
            ]

            for switch_name in switch_names:
                if hasattr(self, switch_name):
                    switch = getattr(self, switch_name)
                    if hasattr(switch, 'configure'):
                        switch.configure(
                            progress_color=self.colors["switch_on"],
                            button_color=self.colors["switch_on"]
                        )
        except Exception as e:
            print(f"Error updating switch colors: {e}")



    def get_config_path(self):
        """Get the path to the config directory"""
        # AppData\Local\FusionClient\Client\
        appdata_local = Path(os.environ.get('LOCALAPPDATA', os.path.expanduser('~/.local/share')))
        config_dir = appdata_local / "FusionClient" / "Client"

        # Create directories if they don't exist
        config_dir.mkdir(parents=True, exist_ok=True)

        return config_dir / "config.json"

    def load_config(self):
        config_file = self.get_config_path()
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                self.hotkeys = config.get('hotkeys', self.hotkeys)

                # Load color theme
                saved_theme = config.get('color_theme', 'Red')
                if saved_theme in self.color_themes:
                    self.current_theme = saved_theme
                    self.colors = self.color_themes[self.current_theme]

                # Load sound settings
                self.sounds_enabled = config.get('sounds_enabled', True)

                # Load module states (all modules deactivated by default)
                module_states = config.get('module_states', {})
                self.is_air_jump_enabled = module_states.get('air_jump', False)
                self.is_fast_ladder_enabled = module_states.get('fast_ladder', False)
                self.is_phase_enabled = module_states.get('phase', False)
                self.is_no_web_enabled = module_states.get('no_web', False)
                self.is_high_jump_enabled = module_states.get('high_jump', False)
                self.is_fly_enabled = module_states.get('fly', False)
                self.is_antikb_enabled = module_states.get('antikb', False)
                self.is_zoom_enabled = module_states.get('zoom', False)
                self.is_see_entities_enabled = module_states.get('see_entities', False)
                self.is_no_hurtcam_enabled = module_states.get('no_hurtcam', False)
                self.is_reach_enabled = module_states.get('reach', False)
                self.is_s_tap_enabled = module_states.get('s_tap', False)
                self.is_semi_scaffold_enabled = module_states.get('semi_scaffold', False)
                self.is_rapid_hit_enabled = module_states.get('rapid_hit', False)
                self.is_auto_clicker_enabled = module_states.get('auto_clicker', False)
                self.is_fast_place_enabled = module_states.get('fast_place', False)
                self.is_double_click_enabled = module_states.get('double_click', False)
                self.is_auto_sprint_enabled = module_states.get('auto_sprint', False)

                # Load module values
                module_values = config.get('module_values', {})
                self.fast_ladder_value = module_values.get('fast_ladder_value', 1.0)
                self.high_jump_value = module_values.get('high_jump_value', 1.0)
                self.reach_value = module_values.get('reach_value', 3.0)
                self.auto_clicker_cps = module_values.get('auto_clicker_cps', 10)
                self.fast_place_cps = module_values.get('fast_place_cps', 10)
                self.zoom_hold_mode = module_values.get('zoom_hold_mode', True)  # Zoom hold activated by default

        except FileNotFoundError:
            # Create default config if file doesn't exist
            self.save_config()
        except json.JSONDecodeError:
            pass

    def save_config(self):
        config_file = self.get_config_path()
        try:
            # Ensure all critical attributes exist before saving
            if not hasattr(self, 'high_jump_value'):
                self.high_jump_value = 1.0
            if not hasattr(self, 'reach_value'):
                self.reach_value = 3.0
            if not hasattr(self, 'auto_clicker_cps'):
                self.auto_clicker_cps = 10
            if not hasattr(self, 'fast_place_cps'):
                self.fast_place_cps = 10
            if not hasattr(self, 'fast_ladder_value'):
                self.fast_ladder_value = 1.0
            if not hasattr(self, 'zoom_hold_mode'):
                self.zoom_hold_mode = True

            config = {
                'hotkeys': getattr(self, 'hotkeys', {}),
                'color_theme': getattr(self, 'current_theme', 'Red'),
                'sounds_enabled': getattr(self, 'sounds_enabled', True),
                'module_states': {
                    'air_jump': getattr(self, 'is_air_jump_enabled', False),
                    'fast_ladder': getattr(self, 'is_fast_ladder_enabled', False),
                    'phase': getattr(self, 'is_phase_enabled', False),
                    'no_web': getattr(self, 'is_no_web_enabled', False),
                    'high_jump': getattr(self, 'is_high_jump_enabled', False),
                    'fly': getattr(self, 'is_fly_enabled', False),
                    'antikb': getattr(self, 'is_antikb_enabled', False),
                    'zoom': getattr(self, 'is_zoom_enabled', False),
                    'see_entities': getattr(self, 'is_see_entities_enabled', False),
                    'no_hurtcam': getattr(self, 'is_no_hurtcam_enabled', False),
                    'reach': getattr(self, 'is_reach_enabled', False),
                    's_tap': getattr(self, 'is_s_tap_enabled', False),
                    'semi_scaffold': getattr(self, 'is_semi_scaffold_enabled', False),
                    'rapid_hit': getattr(self, 'is_rapid_hit_enabled', False),
                    'auto_clicker': getattr(self, 'is_auto_clicker_enabled', False),
                    'fast_place': getattr(self, 'is_fast_place_enabled', False),
                    'double_click': getattr(self, 'is_double_click_enabled', False),
                    'auto_sprint': getattr(self, 'is_auto_sprint_enabled', False)
                },
                'module_values': {
                    'fast_ladder_value': self.fast_ladder_value,
                    'high_jump_value': self.high_jump_value,
                    'reach_value': self.reach_value,
                    'auto_clicker_cps': self.auto_clicker_cps,
                    'fast_place_cps': self.fast_place_cps,
                    'zoom_hold_mode': self.zoom_hold_mode
                }
            }
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=4)
        except Exception as e:
            print(f"Error saving config: {e}")

    def restore_module_states(self):
        """Restore saved module states and update UI switches after Minecraft connection"""
        try:
            # Ensure all critical attributes exist before restoring
            if not hasattr(self, 'high_jump_value'):
                self.high_jump_value = 1.0
            if not hasattr(self, 'reach_value'):
                self.reach_value = 3.0
            if not hasattr(self, 'auto_clicker_cps'):
                self.auto_clicker_cps = 10
            if not hasattr(self, 'fast_place_cps'):
                self.fast_place_cps = 10
            if not hasattr(self, 'fast_ladder_value'):
                self.fast_ladder_value = 1.0

            # Update UI sliders with loaded values first
            if hasattr(self, 'fast_ladder_slider'):
                self.fast_ladder_slider.set(self.fast_ladder_value)
                self.fast_ladder_value_label.configure(text=f"{self.fast_ladder_value:.1f}")

            if hasattr(self, 'high_jump_slider'):
                self.high_jump_slider.set(self.high_jump_value)
                self.high_jump_value_label.configure(text=f"{self.high_jump_value:.1f}")

            if hasattr(self, 'reach_slider'):
                self.reach_slider.set(self.reach_value)
                self.reach_value_label.configure(text=f"{self.reach_value:.1f}")

            if hasattr(self, 'cps_slider'):
                self.cps_slider.set(self.auto_clicker_cps)
                self.cps_label.configure(text=str(self.auto_clicker_cps))

            if hasattr(self, 'fast_place_slider'):
                self.fast_place_slider.set(self.fast_place_cps)
                self.fast_place_value_label.configure(text=str(self.fast_place_cps))

            if hasattr(self, 'zoom_hold_switch'):
                if self.zoom_hold_mode:
                    self.zoom_hold_switch.select()
                else:
                    self.zoom_hold_switch.deselect()

            # Restore module states by setting switches and applying patches
            modules_to_restore = [
                ('air_jump', self.is_air_jump_enabled, self.air_jump_switch, self.toggle_air_jump),
                ('fast_ladder', self.is_fast_ladder_enabled, self.fast_ladder_switch, self.toggle_fast_ladder),
                ('phase', self.is_phase_enabled, self.phase_switch, self.toggle_phase),
                ('no_web', self.is_no_web_enabled, self.no_web_switch, self.toggle_no_web),
                ('high_jump', self.is_high_jump_enabled, self.high_jump_switch, self.toggle_high_jump),
                ('fly', self.is_fly_enabled, self.fly_switch, self.toggle_fly),
                ('antikb', self.is_antikb_enabled, self.antikb_switch, self.toggle_antikb),
                ('zoom', self.is_zoom_enabled, self.zoom_switch, self.toggle_zoom),
                ('see_entities', self.is_see_entities_enabled, self.see_entities_switch, self.toggle_see_entities),
                ('no_hurtcam', self.is_no_hurtcam_enabled, self.no_hurtcam_switch, self.toggle_no_hurtcam),
                ('reach', self.is_reach_enabled, self.reach_switch, self.toggle_reach),
                ('semi_scaffold', self.is_semi_scaffold_enabled, self.semi_scaffold_switch, self.toggle_semi_scaffold),
                ('rapid_hit', self.is_rapid_hit_enabled, self.rapid_hit_switch, self.toggle_rapid_hit),
                ('auto_clicker', self.is_auto_clicker_enabled, self.auto_clicker_switch, self.toggle_auto_clicker),
                ('fast_place', self.is_fast_place_enabled, self.fast_place_switch, self.toggle_fast_place),
                ('double_click', self.is_double_click_enabled, self.double_click_switch, self.toggle_double_click),
                ('auto_sprint', self.is_auto_sprint_enabled, self.auto_sprint_switch, self.toggle_auto_sprint),
                ('s_tap', self.is_s_tap_enabled, self.s_tap_switch, self.toggle_s_tap)
            ]

            for module_name, is_enabled, switch, toggle_func in modules_to_restore:
                if is_enabled and switch is not None:
                    try:
                        # Set the switch to enabled state
                        switch.select()
                        # Apply the module (this will patch memory and set the enabled state)
                        toggle_func()
                        print(f"Restored {module_name} module")
                    except Exception as e:
                        print(f"Failed to restore {module_name}: {e}")
                        # If restoration fails, make sure the switch reflects the actual state
                        switch.deselect()

            # Save config to ensure any changes are persisted
            self.save_config()

        except Exception as e:
            print(f"Error restoring module states: {e}")

    def on_closing(self):
        """Handle application closing - save config and cleanup"""
        try:
            # Save current configuration
            self.save_config()
            print("Configuration saved on exit")

            # Stop hotkey listeners
            self.stop_hotkey_listeners()

            # Destroy the window
            self.root.destroy()
        except Exception as e:
            print(f"Error during application shutdown: {e}")
            # Force close if there's an error
            self.root.destroy()

    def get_sounds_directory(self):
        """Get the path to the sounds directory"""
        # AppData\Local\FusionClient\Sounds\
        appdata_local = Path(os.environ.get('LOCALAPPDATA', os.path.expanduser('~/.local/share')))
        sounds_dir = appdata_local / "FusionClient" / "Sounds"

        # Create directories if they don't exist
        sounds_dir.mkdir(parents=True, exist_ok=True)

        return sounds_dir

    def download_sounds(self):
        """Download enable and disable sounds if they don't exist"""
        if self.sounds_downloaded:
            return True

        sounds_dir = self.get_sounds_directory()
        enable_path = sounds_dir / "ENABLE.wav"
        disable_path = sounds_dir / "DISABLE.wav"

        # Check if both files already exist
        if enable_path.exists() and disable_path.exists():
            self.enable_sound_path = str(enable_path)
            self.disable_sound_path = str(disable_path)
            self.sounds_downloaded = True
            print("Sounds already exist, skipping download")
            return True

        try:
            # Download enable sound if it doesn't exist
            if not enable_path.exists():
                print("Downloading enable sound...")
                response = requests.get(self.enable_sound_url, timeout=10)
                response.raise_for_status()
                with open(enable_path, 'wb') as f:
                    f.write(response.content)
                print("Enable sound downloaded successfully")

            # Download disable sound if it doesn't exist
            if not disable_path.exists():
                print("Downloading disable sound...")
                response = requests.get(self.disable_sound_url, timeout=10)
                response.raise_for_status()
                with open(disable_path, 'wb') as f:
                    f.write(response.content)
                print("Disable sound downloaded successfully")

            # Set paths and mark as downloaded
            self.enable_sound_path = str(enable_path)
            self.disable_sound_path = str(disable_path)
            self.sounds_downloaded = True
            return True

        except Exception as e:
            print(f"Failed to download sounds: {e}")
            return False

    def play_sound(self, sound_type):
        """Play enable or disable sound"""
        if not self.sounds_enabled:
            return

        # Download sounds if not already done
        if not self.sounds_downloaded:
            if not self.download_sounds():
                return

        try:
            if sound_type == "enable" and self.enable_sound_path:
                pygame.mixer.Sound(self.enable_sound_path).play()
            elif sound_type == "disable" and self.disable_sound_path:
                pygame.mixer.Sound(self.disable_sound_path).play()
        except Exception as e:
            print(f"Failed to play {sound_type} sound: {e}")

    def toggle_sounds(self):
        """Toggle sound enable/disable"""
        self.sounds_enabled = self.sound_switch.get()
        print(f"Sounds {'enabled' if self.sounds_enabled else 'disabled'}")

    def handle_hotkey(self, module_name):
        """Unified hotkey handler for all modules"""
        # Set toggle source to 'hotkey' to indicate this toggle was triggered by a hotkey
        self._toggle_source = 'hotkey'

        # Call the appropriate toggle method based on the module name
        if module_name == "air_jump":
            self.toggle_air_jump()
        elif module_name == "fast_ladder":
            self.toggle_fast_ladder()
        elif module_name == "phase":
            self.toggle_phase()
        elif module_name == "no_web":
            self.toggle_no_web()
        elif module_name == "high_jump":
            self.toggle_high_jump()
        elif module_name == "fly":
            self.toggle_fly()
        elif module_name == "antikb":
            self.toggle_antikb()
        elif module_name == "auto_clicker":
            self.toggle_auto_clicker()
        elif module_name == "double_click":
            self.toggle_double_click()
        elif module_name == "semi_scaffold":
            self.toggle_semi_scaffold()
        elif module_name == "rapid_hit":
            self.toggle_rapid_hit()
        elif module_name == "zoom":
            self.toggle_zoom()
        elif module_name == "see_entities":
            self.toggle_see_entities()
        elif module_name == "no_hurtcam":
            self.toggle_no_hurtcam()
        elif module_name == "fast_place":
            self.toggle_fast_place()
        elif module_name == "reach":
            self.toggle_reach()
        elif module_name == "s_tap":
            self.toggle_s_tap()
        elif module_name == "auto_sprint":
            self.toggle_auto_sprint()

        # Reset toggle source
        self._toggle_source = None

    def start_hotkey_listeners(self):
        # Clear any existing listeners first
        self.stop_hotkey_listeners()

        # Set up our custom key monitoring system
        self.hotkey_listener_active = True
        self.key_states = {}  # Track the state of all keys
        self.hotkey_modules = {}  # Map hotkeys to their modules

        # Register all hotkeys
        registered_hotkeys = 0
        for module, hotkey in self.hotkeys.items():
            if hotkey:
                # Check if the module's address is available before registering the hotkey
                if (module == "air_jump" and self.air_jump_address) or \
                   (module == "fast_ladder" and self.fast_ladder_address) or \
                   (module == "phase" and self.phase_address) or \
                   (module == "no_web" and self.no_web_address) or \
                   (module == "high_jump" and self.high_jump_address) or \
                   (module == "fly" and self.fly_address) or \
                   (module == "antikb" and (self.antikb_address1 or self.antikb_address2)) or \
                   (module == "auto_clicker") or \
                   (module == "double_click") or \
                   (module == "s_tap") or \
                   (module == "semi_scaffold" and (self.scaffold_address1 or self.scaffold_address2)) or \
                   (module == "rapid_hit" and self.rapid_hit_address) or \
                   (module == "zoom" and self.zoom_address) or \
                   (module == "see_entities" and self.see_entities_address) or \
                   (module == "no_hurtcam" and self.no_hurtcam_address) or \
                   (module == "fast_place") or \
                   (module == "reach" and self.reach_address) or \
                   (module == "auto_sprint" and self.auto_sprint_address):

                    # Store the mapping of hotkey to module
                    self.hotkey_modules[hotkey] = module
                    registered_hotkeys += 1
                    print(f"Registered hotkey '{hotkey}' for {module}")

        # Start the key monitoring thread if we have registered hotkeys
        if registered_hotkeys > 0:
            self.key_monitor_thread = threading.Thread(target=self.key_monitor, daemon=True)
            self.key_monitor_thread.start()
            # self.status_label.configure(text=f"Status: {registered_hotkeys} Hotkey listeners started")
        else:
            # self.status_label.configure(text="Status: No hotkeys registered. Set hotkeys in Settings")
            pass

    def key_monitor(self):
        """Custom key monitoring thread that handles hotkeys even when other keys are pressed."""
        # Dictionary to track if a hotkey was recently triggered (to prevent rapid toggling)
        last_triggered = {}
        cooldown_time = 0.2  # seconds between allowed triggers

        # Dictionary to track the state of hold-mode hotkeys
        hold_states = {}

        while self.hotkey_listener_active:
            for hotkey, module in self.hotkey_modules.items():
                # Check if the hotkey is currently pressed
                try:
                    if keyboard.is_pressed(hotkey):
                        # Special handling for zoom in hold mode
                        if module == "zoom" and self.zoom_hold_mode:
                            if hotkey not in hold_states or not hold_states[hotkey]:
                                # Key was just pressed, activate zoom
                                if self.zoom_address:
                                    try:
                                        self.pm.write_bytes(self.zoom_address, self.patched_zoom_bytes, len(self.patched_zoom_bytes))
                                        self.is_zoom_enabled = True
                                        # self.status_label.configure(text="Status: Zoom activated (hold)")
                                    except Exception as e:
                                        # self.status_label.configure(text=f"Status: Zoom Error - {str(e)}")
                                        pass
                                hold_states[hotkey] = True
                        else:
                            # Normal toggle behavior for other modules or zoom in toggle mode
                            # Check if this hotkey is not in cooldown
                            current_time = time.time()
                            if hotkey not in last_triggered or (current_time - last_triggered[hotkey]) > cooldown_time:
                                # Trigger the hotkey action
                                self.handle_hotkey(module)
                                last_triggered[hotkey] = current_time
                    else:
                        # Key is not pressed
                        if module == "zoom" and self.zoom_hold_mode and hotkey in hold_states and hold_states[hotkey]:
                            # Key was released, deactivate zoom
                            if self.zoom_address:
                                try:
                                    self.pm.write_bytes(self.zoom_address, self.original_zoom_bytes, len(self.original_zoom_bytes))
                                    self.is_zoom_enabled = False
                                    # self.status_label.configure(text="Status: Zoom deactivated (hold)")
                                except Exception as e:
                                    # self.status_label.configure(text=f"Status: Zoom Error - {str(e)}")
                                    pass
                            hold_states[hotkey] = False
                except Exception as e:
                    print(f"Error checking hotkey {hotkey}: {e}")

            # Small sleep to prevent high CPU usage
            time.sleep(0.01)

    def stop_hotkey_listeners(self):
        # Stop our custom key monitoring system
        self.hotkey_listener_active = False
        if hasattr(self, 'key_monitor_thread') and self.key_monitor_thread.is_alive():
            # Give the thread time to exit gracefully
            time.sleep(0.1)

        # Clear all keyboard hooks just to be safe
        keyboard.unhook_all()
        # self.status_label.configure(text="Status: Hotkey listeners stopped")

    def __del__(self):
        # Cleanup beim Beenden
        self.is_auto_clicker_enabled = False
        try:
            if hasattr(self, 'high_jump_newmem'):
                self.pm.free(self.high_jump_newmem)
        except:
            pass

    def run(self):
        self.root.mainloop()

    def find_pattern(self, pattern, name, offset=None):
        import pymem  # Importiere pymem direkt in der Methode

        if offset:
            try:
                base_address = self.pm.base_address
                target_address = base_address + offset
                found_bytes = self.pm.read_bytes(target_address, len(pattern))
                if found_bytes == pattern:
                    return target_address
            except:
                pass

        try:
            module = pymem.process.module_from_name(self.pm.process_handle, "Minecraft.Windows.exe")
            if not module:
                return None

            # Erweitere den Suchbereich
            start_address = module.lpBaseOfDll
            end_address = start_address + module.SizeOfImage

            # Kleinere Blöcke für genauere Suche
            block_size = 0x1000  # 4KB Blöcke
            current_address = start_address

            while current_address < end_address:
                try:
                    # Lese einen Block
                    mem_block = self.pm.read_bytes(current_address, min(block_size, end_address - current_address))

                    # Suche nach dem Pattern
                    offset = mem_block.find(pattern)
                    if offset != -1:
                        found_address = current_address + offset

                        # Verifiziere die gefundene Adresse
                        try:
                            # Lese die Bytes an der gefundenen Stelle
                            verify_bytes = self.pm.read_bytes(found_address, len(pattern))
                            if verify_bytes == pattern:
                                # Print-Statement entfernt - wird in parallel_pattern_search() ausgegeben
                                return found_address
                        except:
                            pass

                except:
                    pass

                current_address += block_size

        except Exception as e:
            print(f"Fehler in find_pattern für {name}: {e}")

        return None

    def parallel_pattern_search(self, patterns):
        """Ultra-schnelle parallele Pattern-Suche"""
        results = {}
        threads = []
        stop_event = threading.Event()  # Event to signal threads to stop

        def search_thread(pattern, name):
            try:
                result = self.find_pattern(pattern, name)
                if result:
                    results[name] = result
                    print(f"Pattern {name} gefunden bei {hex(result)}")
            except Exception as e:
                print(f"Fehler bei der Suche nach {name}: {e}")

        # Starte alle Threads gleichzeitig
        for name, pattern in patterns.items():
            thread = threading.Thread(target=search_thread, args=(pattern, name))
            thread.daemon = True
            thread.start()
            threads.append(thread)

        # Warte maximal 5 Sekunden auf Ergebnisse
        timeout = 5.0
        start_time = time.time()

        while time.time() - start_time < timeout:
            if len(results) == len(patterns):
                break
            time.sleep(0.01)

        # Setze das Stop-Event
        stop_event.set()

        # Warte auf alle Threads
        for thread in threads:
            thread.join(timeout=0.1)  # Warte maximal 0.1 Sekunden pro Thread

        return results





    def toggle_zoom_hold_mode(self):
        self.zoom_hold_mode = self.zoom_hold_switch.get()
        if self.zoom_hold_mode:
            # self.status_label.configure(text="Status: Zoom Hold Mode enabled")
            pass
        else:
            # self.status_label.configure(text="Status: Zoom Hold Mode disabled")
            pass
        # Save config when value changes
        self.save_config()

    def toggle_zoom(self):
        if not self.pm or not self.zoom_address:
            self.zoom_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                # If in hold mode, don't toggle the switch, just apply while key is pressed
                if self.zoom_hold_mode:
                    # This will be handled by the key_monitor method
                    pass
                else:
                    # Toggle mode - switch between states
                    current_state = self.zoom_switch.get()
                    if current_state:
                        self.zoom_switch.deselect()
                    else:
                        self.zoom_switch.select()

            # Apply changes based on the switch state
            if self.zoom_switch.get():
                # Activation: Write NOPs
                self.pm.write_bytes(self.zoom_address, self.patched_zoom_bytes, len(self.patched_zoom_bytes))
                self.is_zoom_enabled = True
                self.play_sound("enable")
                # self.status_label.configure(text="Status: Zoom enabled")
            else:
                # Deactivation: Restore original bytes
                self.pm.write_bytes(self.zoom_address, self.original_zoom_bytes, len(self.original_zoom_bytes))
                self.is_zoom_enabled = False
                self.play_sound("disable")
                # self.status_label.configure(text="Status: Zoom disabled")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text=f"Status: Zoom Error - {str(e)}")
            self.zoom_switch.deselect()
            self._toggle_source = None  # Reset toggle source

    def toggle_see_entities(self):
        if not self.pm or not self.see_entities_address:
            self.see_entities_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.see_entities_switch.get()
                if current_state:
                    self.see_entities_switch.deselect()
                else:
                    self.see_entities_switch.select()

            # Apply changes based on the switch state
            if self.see_entities_switch.get():
                # Activation: Write NOPs
                self.pm.write_bytes(self.see_entities_address, self.patched_see_entities_bytes, len(self.patched_see_entities_bytes))
                self.is_see_entities_enabled = True
                self.play_sound("enable")
                # self.status_label.configure(text="Status: See Entities enabled")
            else:
                # Deactivation: Restore original bytes
                self.pm.write_bytes(self.see_entities_address, self.original_see_entities_bytes, len(self.original_see_entities_bytes))
                self.is_see_entities_enabled = False
                self.play_sound("disable")
                # self.status_label.configure(text="Status: See Entities disabled")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text=f"Status: See Entities Error - {str(e)}")
            self.see_entities_switch.deselect()
            self._toggle_source = None  # Reset toggle source

    def toggle_no_hurtcam(self):
        if not self.pm or not self.no_hurtcam_address:
            self.no_hurtcam_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.no_hurtcam_switch.get()
                if current_state:
                    self.no_hurtcam_switch.deselect()
                else:
                    self.no_hurtcam_switch.select()

            # Apply changes based on the switch state
            if self.no_hurtcam_switch.get():
                # Activation: Write NOPs + 0x89
                self.pm.write_bytes(self.no_hurtcam_address, self.patched_no_hurtcam_bytes, len(self.patched_no_hurtcam_bytes))
                self.is_no_hurtcam_enabled = True
                self.play_sound("enable")
                # self.status_label.configure(text="Status: No HurtCam enabled")
            else:
                # Deactivation: Restore original bytes
                self.pm.write_bytes(self.no_hurtcam_address, self.original_no_hurtcam_bytes, len(self.original_no_hurtcam_bytes))
                self.is_no_hurtcam_enabled = False
                self.play_sound("disable")
                # self.status_label.configure(text="Status: No HurtCam disabled")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text=f"Status: No HurtCam Error - {str(e)}")
            self.no_hurtcam_switch.deselect()
            self._toggle_source = None  # Reset toggle source



    def ui_toggle_antikb(self):
        self._toggle_source = 'ui'
        self.toggle_antikb()

    def ui_toggle_fast_place(self):
        self._toggle_source = 'ui'
        self.toggle_fast_place()

    def toggle_fast_place_slider(self, event=None):
        if hasattr(self, 'fast_place_value_frame'):
            if self.fast_place_value_frame.winfo_manager():
                self.fast_place_value_frame.pack_forget()
            else:
                self.fast_place_value_frame.pack(fill="x", pady=5, after=self.fast_place_switch)
                self.fast_place_slider.set(self.fast_place_cps)

    def update_fast_place_value(self, value):
        self.fast_place_cps = int(value)
        self.fast_place_value_label.configure(text=f"{self.fast_place_cps}")
        # Save config when value changes
        self.save_config()

    def fast_place_loop(self):
        last_click_time = time.time()
        while True:
            if not self.is_fast_place_enabled:
                break

            current_time = time.time()

            # Prüfe ob das Minecraft-Fenster aktiv ist
            try:
                # Hole das aktive Fenster
                hwnd = win32gui.GetForegroundWindow()
                # Hole den Fenstertitel
                window_title = win32gui.GetWindowText(hwnd)

                # Prüfe ob es das Minecraft-Fenster ist
                if "Minecraft" in window_title:
                    # Prüfe ob die rechte Maustaste gedrückt ist (0x8000 = gedrückt)
                    if win32api.GetAsyncKeyState(0x02) & 0x8000:
                        if current_time - last_click_time >= (1.0 / self.fast_place_cps):
                            # Sende Klick-Events direkt
                            win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0)
                            win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0)
                            last_click_time = current_time
            except Exception as e:
                print(f"Fehler beim Prüfen des Fensters: {e}")

            time.sleep(0.001)  # Minimale Pause zur CPU-Entlastung

    def toggle_fast_place(self):
        if not self.pm:
            self.fast_place_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.fast_place_switch.get()
                if current_state:
                    self.fast_place_switch.deselect()
                else:
                    self.fast_place_switch.select()

            if self.fast_place_switch.get():
                self.is_fast_place_enabled = True

                # Starte neuen Thread
                if not self.fast_place_thread or not self.fast_place_thread.is_alive():
                    self.fast_place_thread = threading.Thread(target=self.fast_place_loop, daemon=True)
                    self.fast_place_thread.start()
                    self.play_sound("enable")
                    # self.status_label.configure(text=f"Status: FastPlace enabled - {self.fast_place_cps} CPS")
            else:
                self.is_fast_place_enabled = False
                self.play_sound("disable")
                # self.status_label.configure(text="Status: FastPlace disabled")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text=f"Status: FastPlace Error - {str(e)}")
            self.fast_place_switch.deselect()
            self._toggle_source = None  # Reset toggle source



    def ui_toggle_double_click(self):
        self._toggle_source = 'ui'
        self.toggle_double_click()

    def toggle_double_click(self):
        if not self.pm:
            self.double_click_switch.deselect()
            self._toggle_source = None  # Reset toggle source
            return

        try:
            # Toggle the switch state if called via hotkey
            if hasattr(self, '_toggle_source') and self._toggle_source == 'hotkey':
                current_state = self.double_click_switch.get()
                if current_state:
                    self.double_click_switch.deselect()
                else:
                    self.double_click_switch.select()

            if self.double_click_switch.get():
                self.is_double_click_enabled = True
                # Start new thread if not already running
                if not self.double_click_thread or not self.double_click_thread.is_alive():
                    self.double_click_thread = threading.Thread(target=self.double_click_loop, daemon=True)
                    self.double_click_thread.start()
                    self.play_sound("enable")
                # self.status_label.configure(text="Status: Double Click enabled")
            else:
                self.is_double_click_enabled = False
                self.play_sound("disable")
                # self.status_label.configure(text="Status: Double Click disabled")

            self._toggle_source = None  # Reset toggle source
        except Exception as e:
            # self.status_label.configure(text=f"Status: Double Click Error - {str(e)}")
            self.double_click_switch.deselect()
            self._toggle_source = None  # Reset toggle source

    def double_click_loop(self):
        last_button_state = False  # Track the previous button state
        while True:
            if not self.is_double_click_enabled:
                break

            try:
                # Check if Minecraft window is active
                hwnd = win32gui.GetForegroundWindow()
                window_title = win32gui.GetWindowText(hwnd)

                if "Minecraft" in window_title:
                    # Get current button state
                    current_button_state = win32api.GetAsyncKeyState(0x01) & 0x8000

                    # Check for button press (transition from not pressed to pressed)
                    if current_button_state and not last_button_state:
                        current_time = time.time()

                        # Only trigger if enough time has passed since last click
                        if current_time - self.last_left_click_time >= 0.1:  # 100ms cooldown
                            # First click (original)
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)

                            # Small delay before second click
                            time.sleep(self.double_click_delay)

                            # Second click
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)

                            self.last_left_click_time = current_time

                    # If button is released, stop double-clicking
                    elif not current_button_state and last_button_state:
                        # Release the button if it was still held
                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)

                    # Update last button state
                    last_button_state = current_button_state
            except Exception as e:
                print(f"Error in double click loop: {e}")

            time.sleep(0.001)  # Minimal pause to reduce CPU usage





if __name__ == "__main__":
    app = FusionClient()
    app.run()