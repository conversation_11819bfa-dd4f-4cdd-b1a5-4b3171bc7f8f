import pymem
import time

def search_rapid_hit_patterns():
    """Suche nach verschiedenen RapidHit Pattern-Variationen"""
    try:
        print("Verbinde mit Minecraft...")
        pm = pymem.Pymem('Minecraft.Windows.exe')
        print(f"Verbunden! Base Address: {hex(pm.base_address)}")
        
        # Hole das Hauptmodul
        module = pymem.process.module_from_name(pm.process_handle, 'Minecraft.Windows.exe')
        if not module:
            print("Modul nicht gefunden")
            return
        
        print(f"Modul: {hex(module.lpBaseOfDll)} - {hex(module.lpBaseOfDll + module.SizeOfImage)}")
        
        # Verschiedene Pattern-Variationen testen
        patterns_to_test = [
            ("Original", bytes([0x45, 0x38, 0x30, 0x0F, 0x84, 0xC1, 0x00, 0x00, 0x00])),
            ("Alt_E9", bytes([0x45, 0x38, 0x30, 0x0F, 0x84, 0xE9, 0x00, 0x00, 0x00])),
            ("Short_C1", bytes([0x45, 0x38, 0x30, 0x0F, 0x84, 0xC1])),
            ("Short_E9", bytes([0x45, 0x38, 0x30, 0x0F, 0x84, 0xE9])),
            ("Very_Short", bytes([0x45, 0x38, 0x30, 0x0F, 0x84])),
            ("JZ_Short", bytes([0x45, 0x38, 0x30, 0x74])),
            ("JNZ_Short", bytes([0x45, 0x38, 0x30, 0x75])),
            ("Without_45", bytes([0x38, 0x30, 0x0F, 0x84, 0xC1])),
        ]
        
        start_addr = module.lpBaseOfDll
        end_addr = start_addr + module.SizeOfImage
        
        for pattern_name, pattern in patterns_to_test:
            print(f"\n=== Suche Pattern {pattern_name}: {pattern.hex()} ===")
            found = search_single_pattern(pm, pattern, pattern_name, start_addr, end_addr)
            if found:
                print(f"SUCCESS: {pattern_name} gefunden!")
            else:
                print(f"NICHT GEFUNDEN: {pattern_name}")
        
        # Zusätzlich: Suche nach 45 38 30 Sequenzen allgemein
        print(f"\n=== Suche nach allen 45 38 30 Sequenzen ===")
        search_45_38_30_sequences(pm, start_addr, end_addr)
        
    except Exception as e:
        print(f"Fehler: {e}")
        import traceback
        traceback.print_exc()

def search_single_pattern(pm, pattern, name, start_addr, end_addr):
    """Suche nach einem einzelnen Pattern"""
    block_size = 0x10000  # 64KB
    overlap_size = len(pattern) - 1
    current_addr = start_addr
    found_any = False
    
    while current_addr < end_addr:
        try:
            read_size = min(block_size, end_addr - current_addr)
            data = pm.read_bytes(current_addr, read_size)
            
            offset = 0
            while True:
                pos = data.find(pattern, offset)
                if pos == -1:
                    break
                
                addr = current_addr + pos
                try:
                    # Verifiziere
                    verify = pm.read_bytes(addr, len(pattern))
                    if verify == pattern:
                        print(f"  -> Gefunden bei {hex(addr)}")
                        # Lese Kontext
                        context = pm.read_bytes(addr, min(20, len(pattern) + 10))
                        print(f"     Kontext: {context.hex()}")
                        found_any = True
                except:
                    pass
                
                offset = pos + 1
                
        except:
            pass
        
        current_addr += block_size - overlap_size
    
    return found_any

def search_45_38_30_sequences(pm, start_addr, end_addr):
    """Suche nach allen 45 38 30 Sequenzen und zeige was danach kommt"""
    pattern = bytes([0x45, 0x38, 0x30])
    block_size = 0x10000
    current_addr = start_addr
    found_count = 0
    
    while current_addr < end_addr and found_count < 20:  # Max 20 Treffer
        try:
            read_size = min(block_size, end_addr - current_addr)
            data = pm.read_bytes(current_addr, read_size)
            
            offset = 0
            while True:
                pos = data.find(pattern, offset)
                if pos == -1:
                    break
                
                addr = current_addr + pos
                try:
                    # Lese 15 Bytes Kontext
                    context = pm.read_bytes(addr, 15)
                    print(f"45 38 30 bei {hex(addr)}: {context.hex()}")
                    
                    # Analysiere was nach 45 38 30 kommt
                    if len(context) >= 6:
                        next_bytes = context[3:6]
                        if next_bytes.startswith(bytes([0x0F, 0x84])):
                            jump_offset = context[5] if len(context) > 5 else 0
                            print(f"  -> JE (0F 84) mit Offset {hex(jump_offset)}")
                        elif next_bytes[0] == 0x74:
                            print(f"  -> JZ (74)")
                        elif next_bytes[0] == 0x75:
                            print(f"  -> JNZ (75)")
                        else:
                            print(f"  -> Andere: {next_bytes.hex()}")
                    
                    found_count += 1
                    if found_count >= 20:
                        break
                        
                except:
                    pass
                
                offset = pos + 1
                
        except:
            pass
        
        current_addr += block_size - 2  # Kleine Überlappung
    
    print(f"Insgesamt {found_count} '45 38 30' Sequenzen gefunden")

if __name__ == "__main__":
    search_rapid_hit_patterns()
